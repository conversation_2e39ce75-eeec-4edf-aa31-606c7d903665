package com.cleevio.fortraders.application.module.discountcode

import com.cleevio.fortraders.IntegrationTest
import com.cleevio.fortraders.application.module.discountcode.query.AdminGetDiscountCodeQuery
import com.cleevio.fortraders.domain.model.challengeplan.constant.ChallengePlanState
import com.cleevio.fortraders.domain.model.discountcode.constant.DiscountCodeState
import com.cleevio.fortraders.domain.model.discountcode.constant.DiscountUsageType
import com.cleevio.fortraders.domain.model.discountcode.exception.DiscountCodeNotFoundException
import com.cleevio.fortraders.domain.model.tradingaccount.constant.PlatformType
import com.cleevio.fortraders.truncatedShouldBe
import io.kotest.assertions.throwables.shouldThrowExactly
import io.kotest.matchers.collections.shouldContainExactly
import io.kotest.matchers.nulls.shouldBeNull
import io.kotest.matchers.shouldBe
import java.time.LocalDate
import java.util.UUID

class AdminGetDiscountCodeQueryHandlerIT : IntegrationTest({

    "should get discount codes" - {
        val user1 = testDataHelper.getUser(index = 1, email = "<EMAIL>")
        val user2 = testDataHelper.getUser(index = 2, email = "<EMAIL>")

        val challengePlan1 = testDataHelper.getChallengePlan(index = 1, state = ChallengePlanState.PUBLISHED)
        val challengePlan2 = testDataHelper.getChallengePlan(index = 2, state = ChallengePlanState.PUBLISHED)
        val challengePlan3 = testDataHelper.getChallengePlan(index = 3, state = ChallengePlanState.ARCHIVED)

        val discountCode1 = testDataHelper.getDiscountCode(
            code = "CODE-1",
            state = DiscountCodeState.ENABLED,
            usageType = DiscountUsageType.GLOBAL,
            validFrom = LocalDate.parse("2021-01-01"),
            validUntil = LocalDate.parse("2021-01-31"),
            applicableToFirstUserOrderOnly = true,
            platforms = setOf(PlatformType.TRADE_LOCKER, PlatformType.DX_TRADE)
        )
        val discountCode2 = testDataHelper.getDiscountCode(
            code = "CODE-2",
            state = DiscountCodeState.ENABLED,
            usageType = DiscountUsageType.BREACH,
            validFrom = LocalDate.parse("2021-01-24"),
            validUntil = LocalDate.parse("2021-02-27"),
            applicableToFirstUserOrderOnly = false,
            platforms = null
        )
        val discountCode3 = testDataHelper.getDiscountCode(
            code = "CODE-3",
            state = DiscountCodeState.DISABLED,
            usageType = DiscountUsageType.GLOBAL,
            validFrom = LocalDate.parse("2021-02-01"),
            validUntil = LocalDate.parse("2021-02-28"),
            applicableToFirstUserOrderOnly = true,
            platforms = setOf(PlatformType.C_TRADER)
        )

        testDataHelper.getDiscountCodeUser(discountCodeId = discountCode1.id, userId = user1.id)
        testDataHelper.getDiscountCodeUser(discountCodeId = discountCode2.id, userId = user1.id)
        testDataHelper.getDiscountCodeUser(discountCodeId = discountCode2.id, userId = user2.id)

        testDataHelper.getDiscountCodeChallengePlan(discountCodeId = discountCode1.id, challengePlanId = challengePlan1.id)
        testDataHelper.getDiscountCodeChallengePlan(discountCodeId = discountCode1.id, challengePlanId = challengePlan2.id)
        testDataHelper.getDiscountCodeChallengePlan(discountCodeId = discountCode2.id, challengePlanId = challengePlan1.id)
        testDataHelper.getDiscountCodeChallengePlan(discountCodeId = discountCode2.id, challengePlanId = challengePlan3.id)

        "should throw because discount code does not exist" {
            shouldThrowExactly<DiscountCodeNotFoundException> {
                queryBus(AdminGetDiscountCodeQuery(discountCodeId = UUID.randomUUID()))
            }
        }

        "should correctly get" {
            val result = queryBus(
                AdminGetDiscountCodeQuery(
                    discountCodeId = discountCode1.id
                )
            )

            result.id shouldBe discountCode1.id
            result.state shouldBe DiscountCodeState.ENABLED
            result.code shouldBe "CODE-1"
            result.usageType shouldBe DiscountUsageType.GLOBAL
            result.validFrom shouldBe LocalDate.parse("2021-01-01")
            result.validUntil shouldBe LocalDate.parse("2021-01-31")
            result.totalLimit.shouldBeNull()
            result.userLimit.shouldBeNull()
            result.perCountryLimit.shouldBeNull()
            result.createdAt truncatedShouldBe discountCode1.createdAt
            result.updatedAt truncatedShouldBe discountCode1.updatedAt
            result.userEmails shouldContainExactly setOf("<EMAIL>")
            result.totalUses shouldBe 0
            result.challengePlanIds shouldContainExactly setOf(challengePlan1.id, challengePlan2.id)
            result.applicableToFirstUserOrderOnly shouldBe true
            result.platforms shouldBe setOf(PlatformType.TRADE_LOCKER, PlatformType.DX_TRADE)
        }
    }
}, cleanAfterEachWithDataTestCase = false)
