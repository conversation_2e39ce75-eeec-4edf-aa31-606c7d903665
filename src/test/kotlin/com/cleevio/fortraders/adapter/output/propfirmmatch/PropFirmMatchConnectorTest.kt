package com.cleevio.fortraders.adapter.output.propfirmmatch

import com.cleevio.fortraders.ConnectorTest
import com.cleevio.fortraders.domain.model.tradingaccount.constant.PlatformType
import com.cleevio.fortraders.domain.model.transaction.constant.Currency
import io.kotest.matchers.collections.shouldHaveSize
import io.kotest.matchers.shouldBe
import org.mockserver.model.HttpRequest
import org.mockserver.model.HttpResponse
import java.math.BigDecimal
import java.util.UUID

class PropFirmMatchConnectorTest : ConnectorTest({

    // Test data
    val trackingMethod = "test-tracking-method"
    val campaignId = "test-campaign-id"
    val orderId = UUID.randomUUID()
    val amount = BigDecimal("100.50")
    val currency = Currency.USD
    val couponCode = "TEST_COUPON"
    val discountAmount = BigDecimal("10.50")
    val commissionAmount = BigDecimal("5.25")
    val productName = "Test Product"
    val productId = UUID.randomUUID()
    val tradingPlatform = PlatformType.META_TRADER_5
    val accountSize = BigDecimal("1000.00")
    val steps = 2

    val underTest = PropFirmMatchConnector(
        baseUrl = baseUrl,
        proxyConfig = null
    )

    "sendPostback should create correct query parameters" {
        // Setup mock server to respond with 200 OK
        client.`when`(HttpRequest.request()).respond(
            HttpResponse.response()
                .withStatusCode(200)
        )

        // Call the method under test
        underTest.sendPostback(
            trackingMethod = trackingMethod,
            campaignId = campaignId,
            orderId = orderId,
            amount = amount,
            currency = currency,
            couponCode = couponCode,
            discountAmount = discountAmount,
            commissionAmount = commissionAmount,
            productName = productName,
            productId = productId,
            tradingPlatform = tradingPlatform,
            accountSize = accountSize,
            steps = steps
        )

        // Verify the request
        val requests = client.retrieveRecordedRequests(null)
        requests shouldHaveSize 1
        requests[0].let {
            it.path.value shouldBe "/postback"
            it.method shouldBe "POST"

            // Verify query parameters
            val queryParams = it.getQueryStringParameters()
            queryParams.getValues("comment") shouldBe listOf(trackingMethod)
            queryParams.getValues("campaign_id") shouldBe listOf(campaignId)
            queryParams.getValues("order_id") shouldBe listOf(orderId.toString())
            queryParams.getValues("amount") shouldBe listOf(amount.toString())
            queryParams.getValues("currency") shouldBe listOf(currency.isoCode)
            queryParams.getValues("coupon") shouldBe listOf(couponCode)
            queryParams.getValues("custom2") shouldBe listOf(discountAmount.toString())
            queryParams.getValues("custom3") shouldBe listOf(commissionAmount.toString())
            queryParams.getValues("custom4") shouldBe listOf(productName)
            queryParams.getValues("custom5") shouldBe listOf(productId.toString())
            queryParams.getValues("custom6") shouldBe listOf(tradingPlatform.toPropFirmMatchFormat())
            queryParams.getValues("custom7") shouldBe listOf(accountSize.toString())
            queryParams.getValues("custom8") shouldBe listOf(steps.toString())
        }
    }

    "PlatformType.toPropFirmMatchFormat should return correct format" {
        PlatformType.META_TRADER_5.toPropFirmMatchFormat() shouldBe "MT5"
        PlatformType.C_TRADER.toPropFirmMatchFormat() shouldBe "cTrader"
        PlatformType.DX_TRADE.toPropFirmMatchFormat() shouldBe "dxTrade"
    }
})
