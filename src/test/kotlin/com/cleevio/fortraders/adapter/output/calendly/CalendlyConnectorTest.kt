package com.cleevio.fortraders.adapter.output.calendly

import com.cleevio.fortraders.ConnectorTest
import com.cleevio.fortraders.bodyShouldBeJson
import com.cleevio.fortraders.domain.model.user.constant.PreferredLanguage
import io.kotest.matchers.collections.shouldHaveSize
import io.kotest.matchers.shouldBe
import io.mockk.every
import io.mockk.mockk
import org.mockserver.model.HttpRequest
import org.mockserver.model.HttpResponse

class CalendlyConnectorTest : ConnectorTest({

    val calendlyPropertiesMock = mockk<CalendlyProperties>()
    lateinit var underTest: CalendlyConnector

    beforeTest {
        every { calendlyPropertiesMock.baseUrl } returns baseUrl
        every { calendlyPropertiesMock.apiToken } returns "calendly-api-token-123"
        every { calendlyPropertiesMock.organization } returns "test-organization-uuid"

        underTest = CalendlyConnector(
            calendlyProperties = calendlyPropertiesMock
        )
    }

    "should correctly get scheduled event external ID" {
        client.`when`(
            HttpRequest.request()
                .withPath("/scheduled_events/AAAAAAAAAAAAAAAA")
                .withMethod("GET")
        ).respond(
            HttpResponse.response()
                .withStatusCode(200)
                .withHeader("Content-Type", "application/json")
                .withBody(
                    """
                    {
                        "resource": {
                            "calendar_event": {
                                "external_id": "external-calendar-event-123"
                            }
                        }
                    }
                    """.trimIndent()
                )
        )

        val result = underTest.getScheduledEventExternalId("AAAAAAAAAAAAAAAA")

        result shouldBe "external-calendar-event-123"

        val requests = client.retrieveRecordedRequests(null)
        requests shouldHaveSize 1
        requests[0].let {
            it.path.value shouldBe "/scheduled_events/AAAAAAAAAAAAAAAA"
            it.method shouldBe "GET"
            it.containsHeader("Authorization", "Bearer calendly-api-token-123") shouldBe true
        }
    }

    "should correctly get one-time booking URL by first fetching event types" {
        client.`when`(
            HttpRequest.request()
                .withPath("/event_types")
                .withQueryStringParameter("organization", "test-organization-uuid")
                .withMethod("GET")
        ).respond(
            HttpResponse.response()
                .withStatusCode(200)
                .withHeader("Content-Type", "application/json")
                .withBody(
                    """
                    {
                        "collection": [
                            {
                                "uri": "https://api.calendly.com/event_types/ET1_EN",
                                "name": "English Event",
                                "scheduling_url": "https://calendly.com/user/event_en",
                                "locale": "en"
                            },
                            {
                                "uri": "https://api.calendly.com/event_types/ET1_ES",
                                "name": "Spanish Event",
                                "scheduling_url": "https://calendly.com/user/event_es",
                                "locale": "es"
                            }
                        ]
                    }
                    """.trimIndent()
                )
        )

        client.`when`(
            HttpRequest.request()
                .withPath("/scheduling_links")
                .withMethod("POST")
        ).respond(
            HttpResponse.response()
                .withStatusCode(201)
                .withHeader("Content-Type", "application/json")
                .withBody(
                    """
                    {
                        "resource": {
                            "booking_url": "https://calendly.com/d/random-string/some-other-part"
                        }
                    }
                    """.trimIndent()
                )
        )

        val result = underTest.getOneTimeBookingUrl(PreferredLanguage.ENGLISH)

        result shouldBe "https://calendly.com/d/random-string"

        val schedulingLinksRequests = client.retrieveRecordedRequests(HttpRequest.request().withPath("/scheduling_links"))
        schedulingLinksRequests shouldHaveSize 1
        schedulingLinksRequests[0].let {
            it.path.value shouldBe "/scheduling_links"
            it.method shouldBe "POST"
            it.containsHeader("Authorization", "Bearer calendly-api-token-123") shouldBe true
            it.bodyShouldBeJson(
                """
                {
                  "max_event_count": 1,
                  "owner": "https://api.calendly.com/event_types/ET1_EN",
                  "owner_type": "EventType"
                }
                """.trimIndent()
            )
        }
    }
})
