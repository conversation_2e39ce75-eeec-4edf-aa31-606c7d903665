package com.cleevio.fortraders.adapter.output.googlecalendar

import com.cleevio.fortraders.UnitTest
import com.google.api.services.calendar.Calendar
import com.google.api.services.calendar.model.ConferenceData
import com.google.api.services.calendar.model.EntryPoint
import com.google.api.services.calendar.model.Event
import io.kotest.matchers.shouldBe
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify

class GoogleCalendarConnectorTest : UnitTest({

    val googleCalendarId = "<EMAIL>"
    val mockCalendarService = mockk<Calendar>()
    val mockEvents = mockk<Calendar.Events>()
    val mockGet = mockk<Calendar.Events.Get>()
    val underTest = GoogleCalendarConnector(
        mockCalendarService,
        calendarId = googleCalendarId
    )

    beforeTest {
        every { mockCalendarService.events() } returns mockEvents
        every { mockEvents.get(googleCalendarId, any()) } returns mockGet
        every { mockGet.setFields("conferenceData,hangoutLink") } returns mockGet
    }

    "should return recording URL from conference data video entry point" {
        val mockEvent = mockk<Event>()
        val mockConferenceData = mockk<ConferenceData>()
        val mockEntryPoint = mockk<EntryPoint>()

        every { mockGet.execute() } returns mockEvent
        every { mockEvent.conferenceData } returns mockConferenceData
        every { mockConferenceData.entryPoints } returns listOf(mockEntryPoint)
        every { mockEntryPoint.entryPointType } returns "video"
        every { mockEntryPoint.uri } returns "https://zoom.us/j/123456789"
        every { mockEvent.hangoutLink } returns null

        val result = underTest.getEventRecording("external-event-123")

        result shouldBe "https://zoom.us/j/123456789"

        verify {
            mockEvents.get(googleCalendarId, "external-event-123")
            mockGet.fields = "conferenceData,hangoutLink"
            mockGet.execute()
        }
    }

    "should return hangout link when no conference data video entry point" {
        val mockEvent = mockk<Event>()
        val mockConferenceData = mockk<ConferenceData>()
        val mockEntryPoint = mockk<EntryPoint>()

        every { mockGet.execute() } returns mockEvent
        every { mockEvent.conferenceData } returns mockConferenceData
        every { mockConferenceData.entryPoints } returns listOf(mockEntryPoint)
        every { mockEntryPoint.entryPointType } returns "phone"
        every { mockEvent.hangoutLink } returns "https://meet.google.com/xyz-abc-def"

        val result = underTest.getEventRecording("external-event-456")

        result shouldBe "https://meet.google.com/xyz-abc-def"
    }

    "should return null when no recording sources available" {
        val mockEvent = mockk<Event>()

        every { mockGet.execute() } returns mockEvent
        every { mockEvent.conferenceData } returns null
        every { mockEvent.hangoutLink } returns null

        val result = underTest.getEventRecording("external-event-empty")

        result shouldBe null
    }
})
