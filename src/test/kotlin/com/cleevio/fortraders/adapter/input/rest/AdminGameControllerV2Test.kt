package com.cleevio.fortraders.adapter.input.rest

import com.cleevio.fortraders.ControllerTest
import com.cleevio.fortraders.addBearerAuthHeader
import com.cleevio.fortraders.addJsonContent
import com.cleevio.fortraders.application.common.util.toUUID
import com.cleevio.fortraders.application.module.game.command.AdminCreateGameCommand
import com.cleevio.fortraders.application.module.game.command.AdminUpdateGameCommand
import com.cleevio.fortraders.application.module.game.query.AdminGetGameQueryV2
import com.cleevio.fortraders.application.module.game.query.AdminSearchGamesQueryV2
import com.cleevio.fortraders.domain.model.game.constant.GameState
import com.cleevio.fortraders.domain.model.tournamentreward.constant.TournamentRewardType
import com.cleevio.fortraders.jsonContent
import io.mockk.every
import io.mockk.just
import io.mockk.runs
import io.mockk.verify
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.data.domain.PageImpl
import org.springframework.data.domain.PageRequest
import org.springframework.test.web.servlet.get
import org.springframework.test.web.servlet.post
import org.springframework.test.web.servlet.put
import java.time.Instant

@WebMvcTest(AdminGameControllerV2::class)
class AdminGameControllerV2Test : ControllerTest({

    beforeAny {
        every { jwtDecoder.decode(any()) } returns admin.jwt
    }

    "should correctly access create game EP" {
        every { commandBus(any<AdminCreateGameCommand>()) } just runs

        mockMvc.post("/admin-app/v2/games") {
            addBearerAuthHeader(admin.accessToken)
            addJsonContent(
                """
                {
                    "logoFileId" : "123e4567-e89b-12d3-a456-************",
                    "state" : "ENABLED",
                    "name" : "New Stock Game",
                    "groupMt5" : "mt5 group",
                    "description" : "Game description"
                }
                """.trimIndent()
            )
        }.andExpect {
            status { isNoContent() }
        }

        verify {
            commandBus(
                AdminCreateGameCommand(
                    logoFileId = ("123e4567-e89b-12d3-a456-************").toUUID(),
                    state = GameState.ENABLED,
                    groupMt5 = "mt5 group",
                    name = "New Stock Game",
                    description = "Game description"
                )
            )
            jwtDecoder.decode(admin.accessToken)
        }
    }

    "should correctly access update game EP" {
        every { commandBus(any<AdminUpdateGameCommand>()) } just runs

        mockMvc.put("/admin-app/v2/games/f14777fa-c31a-4bdc-9129-94a50741e77c") {
            addBearerAuthHeader(admin.accessToken)
            addJsonContent(
                """
                {
                    "state" : "DISABLED",
                    "groupMt5" : "mt5 group",
                    "name" : "Updated Stock Game",
                    "description" : "Updated game description",
                    "logoFileId" : "32bcc464-e423-4ca0-ac0a-3e22864dd636"
                }
                """.trimIndent()
            )
        }.andExpect {
            status { isNoContent() }
        }

        verify {
            commandBus(
                AdminUpdateGameCommand(
                    id = "f14777fa-c31a-4bdc-9129-94a50741e77c".toUUID(),
                    state = GameState.DISABLED,
                    groupMt5 = "mt5 group",
                    name = "Updated Stock Game",
                    description = "Updated game description",
                    logoFileId = "32bcc464-e423-4ca0-ac0a-3e22864dd636".toUUID()
                )
            )
            jwtDecoder.decode(admin.accessToken)
        }
    }

    "should correctly access search game EP" {

        val gameId = "db22f458-033d-42f6-bcbb-74cc4be53e79".toUUID()
        val logoUrl = "https://cdn.fortraders.devel.cleevio.dev/4a74c585-7982-4faf-902a-f85d854b7e33.png"
        every { queryBus(any<AdminSearchGamesQueryV2>()) } returns PageImpl(
            listOf(
                AdminSearchGamesQueryV2.Result(
                    id = gameId,
                    state = GameState.ENABLED,
                    groupMt5 = "mt5 group",
                    name = "New Stock Game",
                    description = "Game description",
                    logoUrl = logoUrl,
                    createdAt = Instant.parse("2022-12-24T18:15:30Z"),
                    updatedAt = Instant.parse("2023-11-19T15:18:39Z")
                )
            ),
            PageRequest.of(1, 10),
            2
        )

        mockMvc.post("/admin-app/games/search?page=1&size=10") {
            addBearerAuthHeader(admin.accessToken)
            addJsonContent(
                """
                {
                    "states": ["DISABLED", "ENABLED"],
                    "createdAtFrom": "2019-12-24T18:15:30Z",
                    "createdAtTo": "2020-12-24T19:18:20Z"
                }
                """.trimIndent()
            )
        }.andExpect {
            status { isOk() }
            jsonContent(
                jsonContent = """
                {
                    "content": [
                        {
                            "id": "$gameId",
                            "groupMt5": "mt5 group",
                            "state": "ENABLED",
                            "name": "New Stock Game",
                            "description": "Game description",
                            "logoUrl": "$logoUrl",
                            "createdAt": "2022-12-24T18:15:30Z",
                            "updatedAt": "2023-11-19T15:18:39Z"
                        }
                    ],
                    "currentPage": 1,
                    "pageSize": 10,
                    "totalElements": 11,
                    "totalPages": 2
                }
                """.trimIndent()
            )

            verify {
                queryBus(
                    AdminSearchGamesQueryV2(
                        PageRequest.of(1, 10),
                        AdminSearchGamesQueryV2.Filter(
                            states = setOf(GameState.DISABLED, GameState.ENABLED),
                            createdAtFrom = Instant.parse("2019-12-24T18:15:30Z"),
                            createdAtTo = Instant.parse("2020-12-24T19:18:20Z")
                        )
                    )
                )
                jwtDecoder.decode(admin.accessToken)
            }
        }
    }

    "should correctly access get game EP" {
        val gameId = "db22f458-033d-42f6-bcbb-74cc4be53e79".toUUID()
        val logoUrl = "https://cdn.fortraders.devel.cleevio.dev/123e4567-e89b-12d3-a456-************.png"
        val tournamentOneId = "085153ca-e02a-4396-a11a-b0b134b94991".toUUID()
        val coverUrl = "https://cdn.fortraders.devel.cleevio.dev/4a74c585-7982-4faf-902a-f85d854b7e33.png"
        val createdAt = Instant.parse("2022-12-24T18:15:30Z")
        val updatedAt = Instant.parse("2023-01-24T18:15:30Z")
        val startsAt = Instant.parse("2023-05-24T18:15:30Z")
        val endsAt = Instant.parse("2023-06-24T18:15:30Z")

        val tournaments = listOf(
            AdminGetGameQueryV2.TournamentDetails(
                id = tournamentOneId,
                name = "Forex tournament",
                startingBalance = 10000.toBigDecimal(),
                rewardType = TournamentRewardType.CHALLENGES,
                buyInsLimit = 150,
                maxDrawdown = 25,
                rules = "These are the rules of the tournament.",
                startsAt = startsAt,
                endsAt = endsAt,
                coverUrl = coverUrl,
                createdAt = createdAt,
                updatedAt = updatedAt
            )
        )

        every { queryBus(any<AdminGetGameQueryV2>()) } returns
            AdminGetGameQueryV2.Result(
                id = gameId,
                state = GameState.ENABLED,
                mt5Group = "tournament group",
                name = "New Stock Game",
                description = "Game description",
                logoFileId = "771f4152-7a39-48ae-a2a6-196d35c96233".toUUID(),
                logoUrl = logoUrl,
                createdAt = createdAt,
                updatedAt = updatedAt,
                tournaments = tournaments
            )

        mockMvc.get("/admin-app/v2/games/$gameId") {
            addBearerAuthHeader(admin.accessToken)
        }.andExpect {
            status { isOk() }
            jsonContent(
                jsonContent = """
                {
                    "id": "$gameId",
                    "state": "ENABLED",
                    "name": "New Stock Game",
                    "description": "Game description",
                    "mt5Group": "tournament group",
                    "logoFileId": "771f4152-7a39-48ae-a2a6-196d35c96233",
                    "logoUrl": "$logoUrl",
                    "createdAt": "$createdAt",
                    "updatedAt": "$updatedAt",
                    "tournaments": [
                        {
                            "coverUrl": "$coverUrl",
                            "name": "Forex tournament",
                            "rewardType": "CHALLENGES",
                            "startingBalance": 10000,
                            "buyInsLimit": 150,
                            "maxDrawdown": 25,
                            "rules": "These are the rules of the tournament.",
                            "startsAt": "$startsAt",
                            "endsAt": "$endsAt",
                            "createdAt": "$createdAt",
                            "updatedAt": "$updatedAt"
                        }
                    ]
                }
                """.trimIndent()
            )

            verify {
                queryBus(AdminGetGameQueryV2(gameId))
                jwtDecoder.decode(admin.accessToken)
            }
        }
    }
})
