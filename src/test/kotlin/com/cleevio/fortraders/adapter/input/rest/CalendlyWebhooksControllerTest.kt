package com.cleevio.fortraders.adapter.input.rest

import com.cleevio.fortraders.ControllerTest
import com.cleevio.fortraders.application.module.verificationcall.command.CancelVerificationCallCommand
import com.cleevio.fortraders.application.module.verificationcall.command.ConfirmVerificationCallCommand
import com.cleevio.fortraders.toUUID
import io.kotest.extensions.time.withConstantNow
import io.mockk.every
import io.mockk.just
import io.mockk.runs
import io.mockk.verify
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.http.MediaType
import org.springframework.test.web.servlet.post
import java.time.Instant
import java.util.UUID

@WebMvcTest(CalendlyWebhooksController::class)
class CalendlyWebhooksControllerTest : ControllerTest({

    "should process invitee.created event and confirm verification call" {
        every { commandBus(any<ConfirmVerificationCallCommand>()) } just runs

        val verificationCallId = UUID.fromString("02787fec-4965-472d-9e7c-9e16503dd4b8")
        val requestBody = """
            {
                "event": "invitee.created",
                "payload": {
                    "uri": "https://api.calendly.com/scheduled_events/AAAAAAAAAAAAAAAA/invitees/BBBBBBBBBBBBBBBB",
                    "tracking": {
                        "utm_content": "$verificationCallId"
                    },
                    "scheduled_event": {
                        "uri": "https://api.calendly.com/scheduled_events/AAAAAAAAAAAAAAAA",
                        "start_time": "2023-12-01T10:00:00.000000Z",
                        "end_time": "2023-12-01T10:30:00.000000Z",
                        "location": {
                            "join_url": "https://zoom.us/j/123456789",
                            "type": "zoom",
                            "status": "active"
                        }
                    }
                }
            }
        """.trimIndent()

        withConstantNow(Instant.ofEpochSecond(1672531200)) {
            mockMvc.post("/webhooks/calendly/events") {
                contentType = MediaType.APPLICATION_JSON
                header(
                    "Calendly-Webhook-Signature",
                    "t=1672531200,v1=7e808797885cad3a47d021373f9cd302fff14c5c4e2c6dffbb630842bb77eb88"
                )
                content = requestBody
            }.andExpect {
                status { isNoContent() }
            }
        }

        verify {
            commandBus(
                ConfirmVerificationCallCommand(
                    verificationCallId = verificationCallId,
                    startTime = Instant.parse("2023-12-01T10:00:00.000000Z"),
                    endTime = Instant.parse("2023-12-01T10:30:00.000000Z"),
                    joinUrl = "https://zoom.us/j/123456789",
                    scheduledEventId = "AAAAAAAAAAAAAAAA"
                )
            )
        }
    }

    "should process invitee.canceled event and cancel verification call" {
        every { commandBus(any<CancelVerificationCallCommand>()) } just runs

        val requestBody = """
            {
                "event": "invitee.canceled",
                "payload": {
                    "uri": "https://api.calendly.com/scheduled_events/CCCCCCCCCCCCCCCC/invitees/DDDDDDDDDDDDDDDD",
                    "tracking": {
                        "utm_content": "${1.toUUID()}"
                    },
                    "scheduled_event": {
                        "uri": "https://api.calendly.com/scheduled_events/CCCCCCCCCCCCCCCC",
                        "start_time": "2023-12-01T14:00:00.000000Z",
                        "end_time": "2023-12-01T14:30:00.000000Z",
                        "location": {
                            "join_url": "https://zoom.us/j/987654321",
                            "type": "zoom",
                            "status": "active"
                        }
                    }
                }
            }
        """.trimIndent()

        withConstantNow(Instant.ofEpochSecond(1672531200)) {
            mockMvc.post("/webhooks/calendly/events") {
                contentType = MediaType.APPLICATION_JSON
                header(
                    "Calendly-Webhook-Signature",
                    "t=1672531200,v1=7cf15821fa653a5ccda8fd183d9b3f687fab227823fd851def2314d12fc61567"
                )
                content = requestBody
            }.andExpect {
                status { isNoContent() }
            }
        }

        verify {
            commandBus(
                CancelVerificationCallCommand(
                    verificationCallId = 1.toUUID()
                )
            )
        }
    }

    "when utmContent is null - should ignore webhook request, log and return" {
        val requestBody = """
            {
                "event": "invitee.created",
                "payload": {
                    "uri": "https://api.calendly.com/scheduled_events/AAAAAAAAAAAAAAAA/invitees/BBBBBBBBBBBBBBBB",
                    "tracking": {},
                    "scheduled_event": {
                        "uri": "https://api.calendly.com/scheduled_events/AAAAAAAAAAAAAAAA",
                        "start_time": "2023-12-01T10:00:00.000000Z",
                        "end_time": "2023-12-01T10:30:00.000000Z",
                        "location": {
                            "join_url": "https://zoom.us/j/123456789",
                            "type": "zoom",
                            "status": "active"
                        }
                    }
                }
            }
        """.trimIndent()

        withConstantNow(Instant.ofEpochSecond(1672531200)) {
            mockMvc.post("/webhooks/calendly/events") {
                contentType = MediaType.APPLICATION_JSON
                header(
                    "Calendly-Webhook-Signature",
                    "t=1672531200,v1=10645c525b1b8284be62587f42cd39c76ef1b2ce075d3a11a6a939fc4ed8366b"
                )
                content = requestBody
            }.andExpect {
                status { isNoContent() }
            }
        }

        verify(exactly = 0) { commandBus(any<ConfirmVerificationCallCommand>()) }
    }
})
