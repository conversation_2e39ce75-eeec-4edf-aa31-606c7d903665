package com.cleevio.fortraders.application.module.game.query

import com.cleevio.fortraders.application.common.query.Query
import com.cleevio.fortraders.domain.model.game.constant.GameState
import io.swagger.v3.oas.annotations.media.Schema
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import java.time.Instant
import java.util.UUID

data class AdminSearchGamesQueryV2(
    val pageable: Pageable,
    val filter: Filter,
) : Query<Page<AdminSearchGamesQueryV2.Result>> {

    data class Filter(
        val states: Set<GameState>?,
        val createdAtFrom: Instant?,
        val createdAtTo: Instant?,
    )

    @Schema(name = "AdminSearchGamesResultV2")
    data class Result(
        val id: UUID,
        val state: GameState,
        val groupMt5: String?,
        val name: String,
        val description: String,
        val logoUrl: String,
        val createdAt: Instant,
        val updatedAt: Instant,
    )
}
