package com.cleevio.fortraders.application.module.transaction.port.output

import com.cleevio.fortraders.domain.model.transaction.constant.Currency
import com.cleevio.fortraders.domain.model.transaction.constant.PaymentGatewayProvider
import com.cleevio.fortraders.domain.model.transaction.constant.TransactionStatus
import java.math.BigDecimal
import java.util.UUID

interface ExecutePayment {
    operator fun invoke(
        paymentId: String,
        transactionId: UUID,
        amount: BigDecimal,
        currency: Currency,
        firstName: String?,
        lastName: String?,
        email: String,
        fullPhoneNumber: String?,
        isSubscription: Boolean,
    ): Result<ExecutePaymentResult>
}

data class ExecutePaymentResult(
    val paymentGatewayProvider: PaymentGatewayProvider,
    val paymentUrl: String?,
    val paymentId: String?,
    val subscriptionId: String?,
    val transactionStatus: TransactionStatus? = null,
)
