package com.cleevio.fortraders.application.module.user.command

import com.cleevio.fortraders.application.common.command.Command
import com.cleevio.fortraders.application.common.validation.CountryCode
import com.cleevio.fortraders.application.common.validation.IP
import java.util.UUID

data class UpdateUserLastLoginIpCommand(
    val userId: UUID,
    @field:IP val lastLoginIp: String,
    @field:CountryCode val countryIsoCode: String?,
) : Command<Unit>
