package com.cleevio.fortraders.application.module.tournamentuser.query

import com.cleevio.fortraders.application.common.query.Query
import com.cleevio.fortraders.domain.model.tournamentuser.constant.TournamentUserState
import com.cleevio.fortraders.domain.model.user.constant.UserKycState
import io.swagger.v3.oas.annotations.media.Schema
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import java.math.BigDecimal
import java.time.Instant
import java.util.UUID

data class AdminSearchTournamentUsersQuery(
    val tournamentId: UUID,
    val gameId: UUID,
    val pageable: Pageable,
) : Query<Page<AdminSearchTournamentUsersQuery.Result>> {

    @Schema(name = "AdminSearchTournamentUsersResult")
    data class Result(
        val tournamentId: UUID,
        val id: UUID,
        val userId: UUID,
        val email: String,
        val firstName: String?,
        val lastName: String?,
        val state: TournamentUserState,
        val kycState: UserKycState,
        val profileImageUrl: String?,
        val profit: BigDecimal?,
        val profitPercentage: BigDecimal?,
        val createdAt: Instant,
        val country: CountryDetail?,
    )

    @Schema(name = "AdminSearchTournamentUsersResultCountryDetailV2")
    data class CountryDetail(
        val id: UUID,
        val name: String,
        val isoCode: String,
    )
}
