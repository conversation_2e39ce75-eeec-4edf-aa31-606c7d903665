package com.cleevio.fortraders.adapter.input.rest

import com.cleevio.fortraders.adapter.input.rest.dto.AdminCreateTournamentsRequestV2
import com.cleevio.fortraders.adapter.input.rest.dto.AdminUpdateTournamentsRequestV2
import com.cleevio.fortraders.application.common.command.CommandBus
import com.cleevio.fortraders.application.common.dto.SimplePage
import com.cleevio.fortraders.application.common.dto.toSimplePage
import com.cleevio.fortraders.application.common.query.QueryBus
import com.cleevio.fortraders.application.module.tournament.query.AdminGetTournamentQueryV2
import com.cleevio.fortraders.application.module.tournamentuser.query.AdminSearchTournamentUsersQuery
import io.swagger.v3.oas.annotations.tags.Tag
import org.springdoc.core.annotations.ParameterObject
import org.springframework.data.domain.Pageable
import org.springframework.http.HttpStatus
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.PutMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.ResponseStatus
import org.springframework.web.bind.annotation.RestController
import java.util.UUID

@Tag(name = "Admin tournaments")
@RestController
@RequestMapping("/admin-app/v2/games/{gameId}/tournaments")
class AdminTournamentsControllerV2(
    private val queryBus: QueryBus,
    private val commandBus: CommandBus,
) {

    @PostMapping
    @ResponseStatus(HttpStatus.NO_CONTENT)
    fun adminCreateTournamentV2(@PathVariable gameId: UUID, @RequestBody request: AdminCreateTournamentsRequestV2): Unit =
        commandBus(request.toCommand(gameId = gameId))

    @PutMapping("/{tournamentId}")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    fun adminUpdateTournamentV2(
        @PathVariable gameId: UUID,
        @PathVariable tournamentId: UUID,
        @RequestBody request: AdminUpdateTournamentsRequestV2,
    ): Unit = commandBus(request.toCommand(gameId = gameId, tournamentId = tournamentId))

    @GetMapping("/{tournamentId}")
    fun adminGetTournamentV2(@PathVariable gameId: UUID, @PathVariable tournamentId: UUID): AdminGetTournamentQueryV2.Result =
        queryBus(AdminGetTournamentQueryV2(tournamentId = tournamentId))

    @PostMapping("/{tournamentId}/users")
    fun adminSearchTournamentUsers(
        @PathVariable gameId: UUID,
        @PathVariable tournamentId: UUID,
        @ParameterObject @PageableDefaultSort pageable: Pageable,
    ): SimplePage<AdminSearchTournamentUsersQuery.Result> = queryBus(
        AdminSearchTournamentUsersQuery(tournamentId = tournamentId, gameId = gameId, pageable = pageable)
    ).toSimplePage()
}
