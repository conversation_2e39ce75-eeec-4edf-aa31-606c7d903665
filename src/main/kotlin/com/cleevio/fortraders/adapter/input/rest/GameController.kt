package com.cleevio.fortraders.adapter.input.rest

import com.cleevio.fortraders.application.common.query.QueryBus
import com.cleevio.fortraders.application.module.game.query.GetGameQuery
import io.swagger.v3.oas.annotations.tags.Tag
import org.springframework.security.core.annotation.AuthenticationPrincipal
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import java.util.UUID

@Tag(name = "Games")
@RestController
@RequestMapping("/web-app/v2/games")
class GameController(
    private val queryBus: QueryBus,
) {

    @GetMapping("/{gameId}")
    fun getGame(@AuthenticationPrincipal userId: UUID?, @PathVariable gameId: UUID): GetGameQuery.Result =
        queryBus(GetGameQuery(gameId = gameId, userId = userId))
}
