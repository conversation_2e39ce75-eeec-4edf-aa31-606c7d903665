package com.cleevio.fortraders.adapter.input.rest

import com.cleevio.fortraders.adapter.input.rest.dto.AdminAddEmailToOrderBlacklistRequest
import com.cleevio.fortraders.adapter.input.rest.dto.AdminSearchOrderBlacklistRequest
import com.cleevio.fortraders.application.common.command.CommandBus
import com.cleevio.fortraders.application.common.dto.SimplePage
import com.cleevio.fortraders.application.common.dto.toSimplePage
import com.cleevio.fortraders.application.common.query.QueryBus
import com.cleevio.fortraders.application.common.util.sanitizeEmail
import com.cleevio.fortraders.application.module.orderblacklist.command.AdminRemoveEmailFromOrderBlacklistCommand
import com.cleevio.fortraders.application.module.orderblacklist.query.AdminSearchOrderBlacklistQuery
import io.swagger.v3.oas.annotations.tags.Tag
import org.springdoc.core.annotations.ParameterObject
import org.springframework.data.domain.Pageable
import org.springframework.http.HttpStatus
import org.springframework.web.bind.annotation.DeleteMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.PutMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.ResponseStatus
import org.springframework.web.bind.annotation.RestController

@Tag(name = "Admin order blacklist")
@RestController
@RequestMapping("/admin-app/order-blacklist")
class AdminOrderBlacklistController(
    private val queryBus: QueryBus,
    private val commandBus: CommandBus,
) {
    @PostMapping("/search")
    fun searchOrderBlacklist(
        @ParameterObject @PageableDefaultSort pageable: Pageable,
        @RequestBody request: AdminSearchOrderBlacklistRequest,
    ): SimplePage<AdminSearchOrderBlacklistQuery.Result> = queryBus(
        request.toQuery(pageable)
    ).toSimplePage()

    @PutMapping()
    @ResponseStatus(HttpStatus.NO_CONTENT)
    fun addEmailToOrderBlacklist(@RequestBody request: AdminAddEmailToOrderBlacklistRequest): Unit =
        commandBus(request.toCommand())

    @DeleteMapping("/{email}")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    fun removeEmailFromOrderBlacklist(@PathVariable email: String): Unit = commandBus(
        AdminRemoveEmailFromOrderBlacklistCommand(
            email = email.sanitizeEmail()
        )
    )
}
