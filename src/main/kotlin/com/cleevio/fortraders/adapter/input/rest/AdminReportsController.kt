package com.cleevio.fortraders.adapter.input.rest

import com.cleevio.fortraders.adapter.input.rest.dto.AdminSearchAccountsReportRequest
import com.cleevio.fortraders.adapter.input.rest.dto.AdminSearchCountriesRevenueOverviewRequest
import com.cleevio.fortraders.adapter.input.rest.dto.AdminSearchInstantAccountPayoutsReportRequest
import com.cleevio.fortraders.adapter.input.rest.dto.AdminSearchOverviewRequest
import com.cleevio.fortraders.adapter.input.rest.dto.AdminSearchPayoutsReportRequest
import com.cleevio.fortraders.adapter.input.rest.dto.AdminSearchProductsRevenueOverviewRequest
import com.cleevio.fortraders.application.common.query.QueryBus
import com.cleevio.fortraders.application.module.report.query.AdminSearchAccountsReportQuery
import com.cleevio.fortraders.application.module.report.query.AdminSearchCountriesRevenueOverviewQuery
import com.cleevio.fortraders.application.module.report.query.AdminSearchInstantAccountPayoutsReportQuery
import com.cleevio.fortraders.application.module.report.query.AdminSearchOverviewQuery
import com.cleevio.fortraders.application.module.report.query.AdminSearchPayoutsReportQuery
import com.cleevio.fortraders.application.module.report.query.AdminSearchProductsRevenueOverviewQuery
import io.swagger.v3.oas.annotations.tags.Tag
import org.springdoc.core.annotations.ParameterObject
import org.springframework.data.domain.Pageable
import org.springframework.data.domain.Sort
import org.springframework.data.web.PageableDefault
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

@Tag(name = "Admin reports")
@RestController
@RequestMapping("/admin-app/reports")
class AdminReportsController(
    private val queryBus: QueryBus,
) {
    @PostMapping("/overview")
    fun searchOverview(
        @ParameterObject pageable: Pageable,
        @RequestBody request: AdminSearchOverviewRequest,
    ): AdminSearchOverviewQuery.Result = queryBus(
        AdminSearchOverviewQuery(
            sort = pageable.sort,
            filter = request.toFilter()
        )
    )

    @PostMapping("/accounts")
    fun searchAccountsReport(@RequestBody request: AdminSearchAccountsReportRequest): AdminSearchAccountsReportQuery.Result =
        queryBus(
            AdminSearchAccountsReportQuery(
                filter = request.toFilter()
            )
        )

    @PostMapping("/payouts")
    fun searchPayoutsReport(
        @ParameterObject @PageableDefault(sort = ["profitAfterSplit"], direction = Sort.Direction.DESC) pageable: Pageable,
        @RequestBody request: AdminSearchPayoutsReportRequest,
    ): AdminSearchPayoutsReportQuery.Result = queryBus(
        AdminSearchPayoutsReportQuery(
            pageable = pageable,
            filter = request.toFilter()
        )
    )

    @PostMapping("/instant-account-payouts")
    fun searchInstantAccountPayoutsReport(
        @ParameterObject @PageableDefault(sort = ["payoutProbability"], direction = Sort.Direction.DESC) pageable: Pageable,
        @RequestBody request: AdminSearchInstantAccountPayoutsReportRequest,
    ): AdminSearchInstantAccountPayoutsReportQuery.Result = queryBus(
        AdminSearchInstantAccountPayoutsReportQuery(
            pageable = pageable,
            filter = request.toFilter()
        )
    )

    @PostMapping("/countries-revenue-overview")
    fun searchCountriesRevenueOverview(
        @RequestBody request: AdminSearchCountriesRevenueOverviewRequest,
    ): AdminSearchCountriesRevenueOverviewQuery.Result = queryBus(
        AdminSearchCountriesRevenueOverviewQuery(
            filter = request.toFilter()
        )
    )

    @PostMapping("/products-revenue-overview")
    fun searchProductsRevenueOverview(
        @RequestBody request: AdminSearchProductsRevenueOverviewRequest,
    ): AdminSearchProductsRevenueOverviewQuery.Result = queryBus(
        AdminSearchProductsRevenueOverviewQuery(
            filter = request.toFilter()
        )
    )
}
