package com.cleevio.fortraders.adapter.output.jooq

import com.cleevio.fortraders.application.module.challenge.port.output.AdminGetChallenge
import com.cleevio.fortraders.application.module.challenge.query.AdminGetChallengeQuery
import com.cleevio.fortraders.domain.model.challenge.exception.ChallengeNotFoundException
import com.cleevio.fortraders.domain.model.challengestepsettingmovement.constant.ChallengeStepSettingMovementType
import com.cleevio.fortraders.public.tables.references.CHALLENGE
import com.cleevio.fortraders.public.tables.references.CHALLENGE_PLAN
import com.cleevio.fortraders.public.tables.references.CHALLENGE_STEP
import com.cleevio.fortraders.public.tables.references.CHALLENGE_STEP_SETTING
import com.cleevio.fortraders.public.tables.references.CHALLENGE_STEP_SETTING_MOVEMENT
import org.jooq.DSLContext
import org.jooq.Record7
import org.jooq.impl.DSL.multiset
import org.jooq.impl.DSL.select
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.math.BigDecimal
import java.util.UUID

@Service
class AdminGetChallengeJooq(private val context: DSLContext) : AdminGetChallenge {

    @Transactional(readOnly = true)
    override fun invoke(challengeId: UUID): AdminGetChallengeQuery.Result {
        val challengeStepSettingMovements = multiset(
            select(
                CHALLENGE_STEP_SETTING_MOVEMENT.ID,
                CHALLENGE_STEP_SETTING_MOVEMENT.TYPE,
                CHALLENGE_STEP_SETTING_MOVEMENT.PERCENTAGE_VALUE,
                CHALLENGE_STEP_SETTING_MOVEMENT.LIST_VALUE,
                CHALLENGE_STEP_SETTING_MOVEMENT.MOVEMENT_PERCENTAGE_VALUE,
                CHALLENGE_STEP_SETTING_MOVEMENT.MOVEMENT_ABSOLUTE_VALUE,
                CHALLENGE_STEP_SETTING_MOVEMENT.IS_DEFAULT
            )
                .from(CHALLENGE_STEP_SETTING_MOVEMENT)
                .where(
                    CHALLENGE_STEP_SETTING_MOVEMENT.CHALLENGE_STEP_SETTING_ID.eq(CHALLENGE_STEP_SETTING.ID)
                        .and(CHALLENGE_STEP_SETTING_MOVEMENT.DELETED_AT.isNull)
                )
        )
        val challengeStepSettings = multiset(
            select(
                CHALLENGE_STEP_SETTING.ID,
                CHALLENGE_STEP_SETTING.TYPE,
                CHALLENGE_STEP_SETTING.IS_VISIBLE_IN_CONFIGURATOR,
                CHALLENGE_STEP_SETTING.USE_MOVEMENTS_FROM_ORDER,
                challengeStepSettingMovements
            )
                .from(CHALLENGE_STEP_SETTING)
                .where(
                    CHALLENGE_STEP_SETTING.CHALLENGE_STEP_ID.eq(CHALLENGE_STEP.ID)
                )
        )
        val challengeSteps = multiset(
            select(
                CHALLENGE_STEP.ID,
                CHALLENGE_STEP.NUMBER,
                CHALLENGE_STEP.TYPE,
                CHALLENGE_STEP.MAX_DRAWDOWN_TYPE,
                CHALLENGE_STEP.DAILY_DRAWDOWN_TYPE,
                CHALLENGE_STEP.DAILY_PAUSE_TYPE,
                CHALLENGE_STEP.LEVERAGE,
                CHALLENGE_STEP.INACTIVITY_PERIOD_DAYS,
                CHALLENGE_STEP.MIN_PROFITABLE_TRADING_DAYS,
                CHALLENGE_STEP.MAX_TRADING_DAYS,
                CHALLENGE_STEP.GROUP_MT5,
                CHALLENGE_STEP.GROUP_TRADE_LOCKER,
                CHALLENGE_STEP.GROUP_CTRADER,
                CHALLENGE_STEP.GROUP_DXTRADE,
                CHALLENGE_STEP.CONSISTENCY_TARGET,
                CHALLENGE_STEP.CREATED_AT,
                CHALLENGE_STEP.UPDATED_AT,
                challengeStepSettings
            )
                .from(CHALLENGE_STEP)
                .where(CHALLENGE_STEP.CHALLENGE_PLAN_ID.eq(CHALLENGE_PLAN.ID))
                .orderBy(CHALLENGE_STEP.NUMBER)
        )
        val challengePlans = multiset(
            select(
                CHALLENGE_PLAN.ID,
                CHALLENGE_PLAN.CATEGORY,
                CHALLENGE_PLAN.STEPS,
                CHALLENGE_PLAN.EXTERNAL_PRODUCT_ID,
                CHALLENGE_PLAN.STATE,
                CHALLENGE_PLAN.TITLE,
                CHALLENGE_PLAN.BASE_PRICE,
                CHALLENGE_PLAN.IS_SUBSCRIPTION,
                CHALLENGE_PLAN.CREATED_AT,
                CHALLENGE_PLAN.UPDATED_AT,
                CHALLENGE_PLAN.PLATFORMS,
                CHALLENGE_PLAN.CAP_TRAILING_DRAWDOWN_AFTER_PAYOUT,
                CHALLENGE_PLAN.MINIMUM_PAYOUT_LIMIT,
                CHALLENGE_PLAN.MAXIMUM_PAYOUT_LIMIT,
                CHALLENGE_PLAN.IS_BETA,
                challengeSteps
            )
                .from(CHALLENGE_PLAN)
                .where(CHALLENGE_PLAN.CHALLENGE_ID.eq(challengeId))
        )

        return context.select(
            CHALLENGE.ID,
            CHALLENGE.NAME,
            CHALLENGE.STARTING_BALANCE,
            CHALLENGE.STATE,
            CHALLENGE.TYPE,
            CHALLENGE.CREATED_AT,
            CHALLENGE.UPDATED_AT,
            challengePlans
        )
            .from(CHALLENGE)
            .where(CHALLENGE.ID.eq(challengeId))
            .fetchOne()
            ?.map {
                AdminGetChallengeQuery.Result(
                    id = it[CHALLENGE.ID]!!,
                    name = it[CHALLENGE.NAME]!!,
                    startingBalance = it[CHALLENGE.STARTING_BALANCE]!!,
                    state = it[CHALLENGE.STATE]!!,
                    type = it[CHALLENGE.TYPE]!!,
                    createdAt = it[CHALLENGE.CREATED_AT]!!,
                    updatedAt = it[CHALLENGE.UPDATED_AT]!!,
                    plans = it[challengePlans].map { challengePlan ->
                        AdminGetChallengeQuery.AdminChallengePlanResult(
                            id = challengePlan[CHALLENGE_PLAN.ID]!!,
                            category = challengePlan[CHALLENGE_PLAN.CATEGORY]!!,
                            stepCount = challengePlan[CHALLENGE_PLAN.STEPS]!!,
                            externalProductId = challengePlan[CHALLENGE_PLAN.EXTERNAL_PRODUCT_ID],
                            state = challengePlan[CHALLENGE_PLAN.STATE]!!,
                            title = challengePlan[CHALLENGE_PLAN.TITLE]!!,
                            basePrice = challengePlan[CHALLENGE_PLAN.BASE_PRICE]!!,
                            isSubscription = challengePlan[CHALLENGE_PLAN.IS_SUBSCRIPTION]!!,
                            platforms = challengePlan[CHALLENGE_PLAN.PLATFORMS].toPlatformTypes(),
                            capTrailingDrawdownAfterPayout = challengePlan[CHALLENGE_PLAN.CAP_TRAILING_DRAWDOWN_AFTER_PAYOUT]!!,
                            minimumPayoutLimit = challengePlan[CHALLENGE_PLAN.MINIMUM_PAYOUT_LIMIT]!!,
                            maximumPayoutLimit = challengePlan[CHALLENGE_PLAN.MAXIMUM_PAYOUT_LIMIT],
                            isBeta = challengePlan[CHALLENGE_PLAN.IS_BETA]!!,
                            createdAt = challengePlan[CHALLENGE_PLAN.CREATED_AT]!!,
                            updatedAt = challengePlan[CHALLENGE_PLAN.UPDATED_AT]!!,
                            steps = challengePlan[challengeSteps].map { challengeStep ->
                                AdminGetChallengeQuery.AdminChallengeStepResult(
                                    id = challengeStep[CHALLENGE_STEP.ID]!!,
                                    number = challengeStep[CHALLENGE_STEP.NUMBER]!!,
                                    type = challengeStep[CHALLENGE_STEP.TYPE]!!,
                                    maxDrawdownType = challengeStep[CHALLENGE_STEP.MAX_DRAWDOWN_TYPE]!!,
                                    dailyDrawdownType = challengeStep[CHALLENGE_STEP.DAILY_DRAWDOWN_TYPE]!!,
                                    dailyPauseType = challengeStep[CHALLENGE_STEP.DAILY_PAUSE_TYPE]!!,
                                    leverage = challengeStep[CHALLENGE_STEP.LEVERAGE]!!,
                                    inactivityPeriodDays = challengeStep[CHALLENGE_STEP.INACTIVITY_PERIOD_DAYS],
                                    minProfitableTradingDays = challengeStep[CHALLENGE_STEP.MIN_PROFITABLE_TRADING_DAYS]!!,
                                    maxTradingDays = challengeStep[CHALLENGE_STEP.MAX_TRADING_DAYS],
                                    groupMt5 = challengeStep[CHALLENGE_STEP.GROUP_MT5],
                                    groupTradeLocker = challengeStep[CHALLENGE_STEP.GROUP_TRADE_LOCKER],
                                    groupCTrader = challengeStep[CHALLENGE_STEP.GROUP_CTRADER],
                                    groupDxTrade = challengeStep[CHALLENGE_STEP.GROUP_DXTRADE],
                                    consistencyTarget = challengeStep[CHALLENGE_STEP.CONSISTENCY_TARGET],
                                    createdAt = challengeStep[CHALLENGE_STEP.CREATED_AT]!!,
                                    updatedAt = challengeStep[CHALLENGE_STEP.UPDATED_AT]!!,
                                    settings = challengeStep[challengeStepSettings].map { challengeStepSetting ->
                                        AdminGetChallengeQuery.AdminChallengeStepSettingResult(
                                            id = challengeStepSetting[CHALLENGE_STEP_SETTING.ID]!!,
                                            type = challengeStepSetting[CHALLENGE_STEP_SETTING.TYPE]!!,
                                            isVisibleInConfigurator =
                                            challengeStepSetting[CHALLENGE_STEP_SETTING.IS_VISIBLE_IN_CONFIGURATOR]!!,
                                            useMovementsFromOrder =
                                            challengeStepSetting[CHALLENGE_STEP_SETTING.USE_MOVEMENTS_FROM_ORDER]!!,
                                            movements = challengeStepSetting[challengeStepSettingMovements].map(
                                                ::mapChallengeStepSettingMovement
                                            )
                                        )
                                    }
                                )
                            }
                        )
                    }
                )
            } ?: throw ChallengeNotFoundException()
    }

    private fun mapChallengeStepSettingMovement(
        movement: Record7<UUID?, ChallengeStepSettingMovementType?, Int?, String?, Int?, BigDecimal?, Boolean?>,
    ) = AdminGetChallengeQuery.AdminChallengeStepSettingMovementResult(
        id = movement[CHALLENGE_STEP_SETTING_MOVEMENT.ID]!!,
        type = movement[CHALLENGE_STEP_SETTING_MOVEMENT.TYPE]!!,
        percentageValue = movement[CHALLENGE_STEP_SETTING_MOVEMENT.PERCENTAGE_VALUE],
        listValue = movement[CHALLENGE_STEP_SETTING_MOVEMENT.LIST_VALUE],
        movementPercentageValue = movement[CHALLENGE_STEP_SETTING_MOVEMENT.MOVEMENT_PERCENTAGE_VALUE],
        movementAbsoluteValue = movement[CHALLENGE_STEP_SETTING_MOVEMENT.MOVEMENT_ABSOLUTE_VALUE]!!,
        isDefault = movement[CHALLENGE_STEP_SETTING_MOVEMENT.IS_DEFAULT]!!
    )
}
