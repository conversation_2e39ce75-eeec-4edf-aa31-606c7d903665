package com.cleevio.fortraders.adapter.output.jooq

import com.cleevio.fortraders.application.module.challenge.port.output.GetChallenges
import com.cleevio.fortraders.application.module.challenge.query.GetChallengesQuery
import com.cleevio.fortraders.domain.model.challenge.constant.ChallengeState
import com.cleevio.fortraders.domain.model.challengeplan.constant.ChallengePlanCategory
import com.cleevio.fortraders.domain.model.challengeplan.constant.ChallengePlanState
import com.cleevio.fortraders.domain.model.challengestepsetting.constant.ChallengeStepSettingType
import com.cleevio.fortraders.domain.model.challengestepsetting.constant.EVALUATION_STEP_ALLOWED_TYPES
import com.cleevio.fortraders.domain.model.challengestepsetting.constant.FUNDED_ONLY_TYPES
import com.cleevio.fortraders.domain.model.challengestepsettingmovement.constant.ChallengeStepSettingMovementType
import com.cleevio.fortraders.public.tables.references.CHALLENGE
import com.cleevio.fortraders.public.tables.references.CHALLENGE_PLAN
import com.cleevio.fortraders.public.tables.references.CHALLENGE_STEP_SETTING
import com.cleevio.fortraders.public.tables.references.CHALLENGE_STEP_SETTING_MOVEMENT
import org.jooq.DSLContext
import org.jooq.Record7
import org.jooq.impl.DSL.case_
import org.jooq.impl.DSL.field
import org.jooq.impl.DSL.multiset
import org.jooq.impl.DSL.name
import org.jooq.impl.DSL.or
import org.jooq.impl.DSL.select
import org.jooq.impl.DSL.table
import org.jooq.impl.DSL.`when`
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.math.BigDecimal
import java.util.UUID

@Service
class GetChallengesJooq(private val context: DSLContext) : GetChallenges {

    @Transactional(readOnly = true)
    override fun invoke(selectChallengePlanId: UUID?): List<GetChallengesQuery.Result> {
        val challengePlanSuccessorIds = selectChallengePlanId?.let {
            val cteName = "previous_challenge_plan_ids_cte"
            val cteNameSql = name(cteName)
            val cteIdFieldName = "id"

            val cte = cteNameSql
                .fields(cteIdFieldName)
                .`as`(
                    select(CHALLENGE_PLAN.ID)
                        .from(CHALLENGE_PLAN)
                        .where(CHALLENGE_PLAN.ID.eq(it))
                        .unionAll(
                            select(CHALLENGE_PLAN.ID)
                                .from(table(cteNameSql))
                                .join(CHALLENGE_PLAN)
                                .on(
                                    field(name(cteName, cteIdFieldName))
                                        .eq(CHALLENGE_PLAN.PREVIOUS_CHALLENGE_PLAN_ID)
                                )
                        )
                )

            val cteIdField = field(name(cteName, cteIdFieldName), UUID::class.java)
            context.withRecursive(cte)
                .select(cteIdField)
                .from(table(cteNameSql))
                .fetchSet(cteIdField)
        } ?: emptySet()

        val challengeStepSettingMovements = multiset(
            select(
                CHALLENGE_STEP_SETTING_MOVEMENT.ID,
                CHALLENGE_STEP_SETTING_MOVEMENT.TYPE,
                CHALLENGE_STEP_SETTING_MOVEMENT.PERCENTAGE_VALUE,
                CHALLENGE_STEP_SETTING_MOVEMENT.LIST_VALUE,
                CHALLENGE_STEP_SETTING_MOVEMENT.MOVEMENT_PERCENTAGE_VALUE,
                CHALLENGE_STEP_SETTING_MOVEMENT.MOVEMENT_ABSOLUTE_VALUE,
                CHALLENGE_STEP_SETTING_MOVEMENT.IS_DEFAULT
            )
                .from(CHALLENGE_STEP_SETTING_MOVEMENT)
                .where(
                    CHALLENGE_STEP_SETTING_MOVEMENT.CHALLENGE_STEP_SETTING_ID.eq(CHALLENGE_STEP_SETTING.ID)
                        .and(CHALLENGE_STEP_SETTING_MOVEMENT.DELETED_AT.isNull)
                )
                .orderBy(
                    `when`(
                        CHALLENGE_STEP_SETTING_MOVEMENT.PERCENTAGE_VALUE.isNotNull,
                        CHALLENGE_STEP_SETTING_MOVEMENT.PERCENTAGE_VALUE
                    ).asc(),
                    `when`(CHALLENGE_STEP_SETTING_MOVEMENT.LIST_VALUE.isNotNull, CHALLENGE_STEP_SETTING_MOVEMENT.LIST_VALUE).asc()
                )
        )

        val challengeStepSettings = multiset(
            select(
                CHALLENGE_STEP_SETTING.ID,
                CHALLENGE_STEP_SETTING.TYPE,
                CHALLENGE_STEP_SETTING.IS_VISIBLE_IN_CONFIGURATOR,
                challengeStepSettingMovements
            )
                .from(CHALLENGE_STEP_SETTING)
                .join(CHALLENGE_STEP_SETTING.challengeStep)
                .where(
                    or(
                        // fetch everything if challenge has only one step (funded) or fetch allowed types for each step
                        CHALLENGE_PLAN.STEPS.eq(1),
                        CHALLENGE_PLAN.STEPS.greaterThan(1).and(
                            or(
                                CHALLENGE_STEP_SETTING.challengeStep.NUMBER.eq(1)
                                    .and(CHALLENGE_STEP_SETTING.TYPE.`in`(EVALUATION_STEP_ALLOWED_TYPES)),
                                CHALLENGE_STEP_SETTING.challengeStep.NUMBER.eq(CHALLENGE_PLAN.STEPS)
                                    .and(CHALLENGE_STEP_SETTING.TYPE.`in`(FUNDED_ONLY_TYPES))
                            )
                        )
                    )
                        .and(CHALLENGE_STEP_SETTING.challengeStep.CHALLENGE_PLAN_ID.eq(CHALLENGE_PLAN.ID))
                )
                .orderBy(
                    case_(CHALLENGE_STEP_SETTING.TYPE)
                        .`when`(ChallengeStepSettingType.PROFIT_TARGET, 1)
                        .`when`(ChallengeStepSettingType.MAX_DRAWDOWN, 2)
                        .`when`(ChallengeStepSettingType.DAILY_DRAWDOWN, 3)
                        .`when`(ChallengeStepSettingType.DAILY_DRAWDOWN, 4)
                        .`when`(ChallengeStepSettingType.DAILY_PROFIT_CAP, 5)
                        .`when`(ChallengeStepSettingType.PAYOUTS, 6)
                        .`when`(ChallengeStepSettingType.PROFIT_SPLIT, 7)
                        .`when`(ChallengeStepSettingType.PROFIT_SPLIT, 8)
                        .`when`(ChallengeStepSettingType.REFUND, 9)
                        .else_(10)
                        .asc()
                )
        )

        val challengePlanPriorityOrdering = case_()
            .`when`(CHALLENGE_PLAN.STEPS.eq(1), 9999)
            .else_(
                CHALLENGE_PLAN.STEPS.mul(10).plus(
                    case_()
                        .`when`(CHALLENGE_PLAN.CATEGORY.eq(ChallengePlanCategory.STANDARD), 1)
                        .else_(2)
                )
            )

        val challengePlans = multiset(
            select(
                CHALLENGE_PLAN.ID,
                CHALLENGE_PLAN.CATEGORY,
                CHALLENGE_PLAN.TITLE,
                CHALLENGE_PLAN.STEPS,
                CHALLENGE_PLAN.BASE_PRICE,
                CHALLENGE_PLAN.PLATFORMS,
                CHALLENGE_PLAN.IS_SUBSCRIPTION,
                CHALLENGE_PLAN.STEPS,
                CHALLENGE_PLAN.IS_BETA,
                challengeStepSettings
            )
                .from(CHALLENGE_PLAN)
                .where(CHALLENGE_PLAN.CHALLENGE_ID.eq(CHALLENGE.ID))
                .and(CHALLENGE_PLAN.STATE.eq(ChallengePlanState.PUBLISHED))
                .orderBy(challengePlanPriorityOrdering.asc())
        )

        return context.select(
            CHALLENGE.ID,
            CHALLENGE.NAME,
            CHALLENGE.TYPE,
            CHALLENGE.STARTING_BALANCE,
            challengePlans
        )
            .from(CHALLENGE)
            .where(CHALLENGE.STATE.eq(ChallengeState.ENABLED))
            .orderBy(CHALLENGE.STARTING_BALANCE.asc())
            .fetch()
            .map { challenge ->
                val mappedPlans = challenge[challengePlans]!!.map { plan ->
                    val challengePlanId = plan[CHALLENGE_PLAN.ID]!!

                    GetChallengesQuery.ChallengePlanDetail(
                        id = challengePlanId,
                        category = plan[CHALLENGE_PLAN.CATEGORY]!!,
                        title = plan[CHALLENGE_PLAN.TITLE]!!,
                        basePrice = plan[CHALLENGE_PLAN.BASE_PRICE]!!,
                        platforms = plan[CHALLENGE_PLAN.PLATFORMS].toPlatformTypes(),
                        isSubscription = plan[CHALLENGE_PLAN.IS_SUBSCRIPTION]!!,
                        steps = plan[CHALLENGE_PLAN.STEPS]!!,
                        isSelected = challengePlanId in challengePlanSuccessorIds,
                        isBeta = plan[CHALLENGE_PLAN.IS_BETA]!!,
                        settings = plan[challengeStepSettings]!!.map { setting ->
                            GetChallengesQuery.ChallengeStepSettingDetail(
                                type = setting[CHALLENGE_STEP_SETTING.TYPE]!!,
                                isVisibleInConfigurator = setting[CHALLENGE_STEP_SETTING.IS_VISIBLE_IN_CONFIGURATOR]!!,
                                movements = setting[challengeStepSettingMovements].map { movement ->
                                    mapToChallengeStepSettingMovementDetail(movement)
                                }
                            )
                        }
                    )
                }

                GetChallengesQuery.ChallengeDetail(
                    id = challenge[CHALLENGE.ID]!!,
                    name = challenge[CHALLENGE.NAME]!!,
                    type = challenge[CHALLENGE.TYPE]!!,
                    startingBalance = challenge[CHALLENGE.STARTING_BALANCE]!!,
                    plans = mappedPlans,
                    isSelected = mappedPlans.any { it.isSelected }
                )
            }
            .groupBy { it.type }
            .map { (type, challengesInGroup) ->
                GetChallengesQuery.Result(
                    type = type,
                    challenges = challengesInGroup
                )
            }
            .sortedBy { it.type }
    }

    private fun mapToChallengeStepSettingMovementDetail(
        movement: Record7<
            UUID?,
            ChallengeStepSettingMovementType?,
            Int?,
            String?,
            Int?,
            BigDecimal?,
            Boolean?
            >,
    ) = GetChallengesQuery.ChallengeStepSettingMovementDetail(
        id = movement[CHALLENGE_STEP_SETTING_MOVEMENT.ID]!!,
        type = movement[CHALLENGE_STEP_SETTING_MOVEMENT.TYPE]!!,
        percentageValue = movement[CHALLENGE_STEP_SETTING_MOVEMENT.PERCENTAGE_VALUE],
        listValue = movement[CHALLENGE_STEP_SETTING_MOVEMENT.LIST_VALUE],
        movementPercentageValue = movement[CHALLENGE_STEP_SETTING_MOVEMENT.MOVEMENT_PERCENTAGE_VALUE],
        movementAbsoluteValue = movement[CHALLENGE_STEP_SETTING_MOVEMENT.MOVEMENT_ABSOLUTE_VALUE],
        isDefault = movement[CHALLENGE_STEP_SETTING_MOVEMENT.IS_DEFAULT]!!
    )
}
