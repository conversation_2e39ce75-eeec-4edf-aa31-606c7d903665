package com.cleevio.fortraders.adapter.output.jooq.systemsetting

import com.cleevio.fortraders.application.module.systemsetting.port.AdminGetSystemSettings
import com.cleevio.fortraders.application.module.systemsetting.query.AdminGetSystemSettingsQuery
import com.cleevio.fortraders.public.tables.references.SYSTEM_SETTING
import org.jooq.DSLContext
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional

@Component
class GetAllSystemSettingsJooq(
    private val dslContext: DSLContext,
) : AdminGetSystemSettings {

    @Transactional(readOnly = true)
    override fun invoke(): AdminGetSystemSettingsQuery.Result {
        val settings = dslContext
            .select(
                SYSTEM_SETTING.TYPE,
                SYSTEM_SETTING.VALUE,
                SYSTEM_SETTING.DESCRIPTION
            )
            .from(SYSTEM_SETTING)
            .fetch()
            .map { record ->
                val type = record[SYSTEM_SETTING.TYPE]!!

                AdminGetSystemSettingsQuery.AdminSystemSettingItem(
                    type = type,
                    dataType = type.toJSDataType(),
                    value = record[SYSTEM_SETTING.VALUE]!!,
                    description = record[SYSTEM_SETTING.DESCRIPTION]!!
                )
            }

        return AdminGetSystemSettingsQuery.Result(settings = settings)
    }
}
