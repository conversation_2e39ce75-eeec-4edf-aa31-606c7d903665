package com.cleevio.fortraders.adapter.output.tap

import com.cleevio.fortraders.application.module.transaction.port.output.ExecutePayment
import com.cleevio.fortraders.application.module.transaction.port.output.ExecutePaymentResult
import com.cleevio.fortraders.domain.model.transaction.constant.Currency
import com.cleevio.fortraders.domain.model.transaction.constant.PaymentGatewayProvider
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.stereotype.Service
import java.math.BigDecimal
import java.util.UUID

@Service
@ConditionalOnProperty("fortraders.transaction.card-payment-provider", havingValue = "TAP")
class TapExecutePaymentService(
    private val tapConnector: TapConnector,
) : ExecutePayment {
    override fun invoke(
        paymentId: String,
        transactionId: UUID,
        amount: BigDecimal,
        currency: Currency,
        firstName: String?,
        lastName: String?,
        email: String,
        fullPhoneNumber: String?,
        isSubscription: Boolean,
    ): Result<ExecutePaymentResult> = runCatching {
        tapConnector.createCharge(
            sourceId = paymentId,
            transactionId = transactionId,
            amount = amount,
            currency = currency,
            email = email,
            firstName = requireNotNull(firstName),
            lastName = requireNotNull(lastName),
            fullPhoneNumber = fullPhoneNumber
        ).let { (id, url) ->
            ExecutePaymentResult(
                paymentGatewayProvider = PaymentGatewayProvider.TAP,
                paymentUrl = url,
                paymentId = id,
                subscriptionId = null
            )
        }
    }
}
