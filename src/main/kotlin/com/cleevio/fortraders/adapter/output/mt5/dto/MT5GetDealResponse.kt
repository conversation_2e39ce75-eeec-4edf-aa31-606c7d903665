package com.cleevio.fortraders.adapter.output.mt5.dto

import com.cleevio.fortraders.adapter.output.mt5.toTradeSide
import com.cleevio.fortraders.adapter.output.mt5.util.EpochUtc1MillisecondsToInstantConverter
import com.cleevio.fortraders.domain.model.trade.constant.TradeSide
import com.fasterxml.jackson.annotation.JsonProperty
import com.fasterxml.jackson.databind.annotation.JsonDeserialize
import java.math.BigDecimal
import java.time.Instant

data class MT5GetDealResponse(
    @JsonProperty("Deal") val dealId: String,
    @JsonProperty("PositionID") val positionId: String,
    @JsonProperty("Login") val accountId: String,
    @JsonProperty("Order") val order: Int,
    @JsonProperty("Action") val action: Int,
    @JsonProperty("Entry") val entry: Int,
    @JsonProperty("ContractSize") val contractSize: BigDecimal,
    @JsonProperty("Symbol") val symbol: String,
    @JsonProperty("Price") val price: BigDecimal,
    @JsonProperty("Volume") val volume: BigDecimal,
    @JsonProperty("Profit") val profit: BigDecimal,
    @JsonProperty("Comment") val comment: String?,
    @JsonProperty("TimeMsc") @JsonDeserialize(converter = EpochUtc1MillisecondsToInstantConverter::class) val time: Instant,
    @JsonProperty("Commission") val commission: BigDecimal,
    @JsonProperty("Storage") val swap: BigDecimal?,
    @JsonProperty("RateMargin") val exchangeRate: BigDecimal,
) {
    val tradeSide: TradeSide
        get() = action.toTradeSide()
}
