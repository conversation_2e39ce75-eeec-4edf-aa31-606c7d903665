package com.cleevio.fortraders.infrastructure.config

import com.cleevio.fortraders.application.common.command.CommandBus
import com.cleevio.fortraders.application.common.util.generateBase64Hmac
import com.cleevio.fortraders.application.common.util.generateLowerCaseHexHmac
import com.cleevio.fortraders.application.module.user.command.UpdateUserLastLoginIpCommand
import com.cleevio.fortraders.domain.model.user.constant.UserRole
import com.cleevio.fortraders.infrastructure.security.AfterLoginUserUpdateActionFilter
import com.cleevio.fortraders.infrastructure.security.ApiKeyAuthFilter
import com.cleevio.fortraders.infrastructure.security.CalendlySignatureFilter
import com.cleevio.fortraders.infrastructure.security.ConfirmoSignatureFilter
import com.cleevio.fortraders.infrastructure.security.HmacSignatureFilter
import com.cleevio.fortraders.infrastructure.security.LoggingFilter
import com.cleevio.fortraders.infrastructure.security.MyFatoorahSignatureFilter
import com.cleevio.fortraders.infrastructure.security.NuveiSignatureFilter
import com.cleevio.fortraders.infrastructure.security.PaytikoSignatureFilter
import com.cleevio.fortraders.infrastructure.security.StripeSignatureFilter
import com.cleevio.fortraders.infrastructure.security.TapSignatureFilter
import com.cleevio.fortraders.infrastructure.security.TrustPaySignatureFilter
import com.cleevio.fortraders.infrastructure.security.toJwtUserAuthToken
import org.springframework.beans.factory.annotation.Value
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.http.HttpMethod
import org.springframework.http.HttpStatus
import org.springframework.security.config.annotation.method.configuration.EnableMethodSecurity
import org.springframework.security.config.annotation.web.builders.HttpSecurity
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity
import org.springframework.security.config.annotation.web.configuration.WebSecurityCustomizer
import org.springframework.security.config.http.SessionCreationPolicy
import org.springframework.security.oauth2.jwt.Jwt
import org.springframework.security.oauth2.server.resource.web.authentication.BearerTokenAuthenticationFilter
import org.springframework.security.web.SecurityFilterChain
import org.springframework.security.web.authentication.HttpStatusEntryPoint
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter
import org.springframework.security.web.util.matcher.AntPathRequestMatcher
import org.springframework.security.web.util.matcher.OrRequestMatcher
import org.springframework.web.cors.CorsConfiguration
import org.springframework.web.cors.CorsConfigurationSource
import org.springframework.web.cors.UrlBasedCorsConfigurationSource
import javax.crypto.Mac

@Configuration
@EnableWebSecurity
@EnableMethodSecurity(securedEnabled = true, jsr250Enabled = true)
class SecurityConfig(
    private val commandBus: CommandBus,

    @Value("\${fortraders.resources-path}")
    private val resourcesPath: String,

    @Value("\${fortraders.security.request-logging.enabled}")
    private val requestLoggingEnabled: Boolean,

    @Value("\${fortraders.security.webhook.secret-key.firebase}")
    private val firebaseSecretKey: String,

    @Value("\${fortraders.security.webhook.secret-key.sumsub}")
    private val sumsubSecretKey: String,

    @Value("\${fortraders.security.webhook.secret-key.veriff}")
    private val veriffSecretKey: String,

    @Value("\${fortraders.security.webhook.secret-key.trust-pay}")
    private val trustPaySecretKey: String,

    @Value("\${fortraders.security.webhook.secret-key.stripe}")
    private val stripeSecretKey: String,

    @Value("\${fortraders.security.webhook.secret-key.my-fatoorah}")
    private val myFatoorahSecretKey: String,

    @Value("\${fortraders.security.webhook.secret-key.confirmo}")
    private val confirmoSecretKey: String,

    @Value("\${fortraders.security.webhook.secret-key.dx-feed}")
    private val dxFeedSecretKey: String,

    @Value("\${fortraders.security.webhook.secret-key.docuseal}")
    private val docuSealSecretKey: String,

    @Value("\${fortraders.security.webhook.secret-key.paytiko}")
    private val paytikoSecretKey: String,

    @Value("\${fortraders.security.webhook.secret-key.nuvei}")
    private val nuveiSecretKey: String,

    @Value("\${fortraders.security.webhook.secret-key.tap}")
    private val tapSecretKey: String,

    @Value("\${fortraders.security.webhook.secret-key.calendly}")
    private val calendlySecretKey: String,

    @Value("\${fortraders.security.cors.allowed-origins}")
    private val corsAllowedOrigins: List<String>,
) {
    @Bean
    fun corsFilter(): CorsConfigurationSource {
        val config =
            CorsConfiguration().apply {
                allowCredentials = true
                allowedOriginPatterns = corsAllowedOrigins
                allowedHeaders = listOf(CorsConfiguration.ALL)
                allowedMethods = listOf(CorsConfiguration.ALL)
            }

        return UrlBasedCorsConfigurationSource().apply {
            registerCorsConfiguration("/**", config)
        }
    }

    @Bean
    fun webSecurityCustomizer() = WebSecurityCustomizer {
        it.ignoring().requestMatchers(API_DOCS_PATH, ACTUATOR_PATH, "$resourcesPath/**")
    }

    @Bean
    fun filterChain(http: HttpSecurity): SecurityFilterChain = http
        .csrf { it.disable() }
        .cors { it.configurationSource(corsFilter()) }
        .authorizeHttpRequests {
            it
                .requestMatchers(FIREBASE_WEBHOOK_CREATE_USER_MATCHER).permitAll()
                .requestMatchers(SUMSUB_WEBHOOK_MATCHER).permitAll()
                .requestMatchers(VERIFF_WEBHOOK_MATCHER).permitAll()
                .requestMatchers(TRUST_PAY_WEBHOOK_PAYMENT_RESULT_MATCHER).permitAll()
                .requestMatchers(STRIPE_WEBHOOK_EVENTS_MATCHER).permitAll()
                .requestMatchers(MY_FATOORAH_WEBHOOK_EVENTS_MATCHER).permitAll()
                .requestMatchers(DX_FEED_WEBHOOK_EVENTS_MATCHER).permitAll()
                .requestMatchers(CONFIRMO_WEBHOOK_INVOICES_MATCHER).permitAll()
                .requestMatchers(DOCUSEAL_WEBHOOK_FORM_EVENTS_MATCHER).permitAll()
                .requestMatchers(PAYTIKO_WEBHOOK_PAYMENT_RESULT_MATCHER).permitAll()
                .requestMatchers(NUVEI_WEBHOOK_PAYMENT_RESULT_MATCHER).permitAll()
                .requestMatchers(TAP_WEBHOOK_PAYMENT_RESULT_MATCHER).permitAll()
                .requestMatchers(CALENDLY_WEBHOOK_EVENTS_MATCHER).permitAll()
                .requestMatchers(WAITING_LIST_MATCHER).permitAll()
                .requestMatchers(SUPPORT_ROLE_EPS_MATCHER).hasAnyRole(UserRole.ADMIN.name, UserRole.SUPPORT.name)
                .requestMatchers(ADMIN_ROLE_EPS_MATCHER).hasRole(UserRole.ADMIN.name)
                .requestMatchers(SUPER_ADMIN_ROLE_EPS_MATCHER).hasRole(UserRole.ADMIN.name)
                .requestMatchers(HttpMethod.GET, "/web-app/games/tournaments").permitAll()
                .requestMatchers(HttpMethod.GET, "/web-app/games/{gameId}").permitAll()
                .requestMatchers(HttpMethod.GET, "/web-app/games/{gameId}/tournaments/{tournamentId}").permitAll()
                .requestMatchers(HttpMethod.GET, "/web-app/challenges").permitAll()
                .requestMatchers(HttpMethod.POST, "/web-app/challenges/{challengeId}/price").permitAll()
                .requestMatchers(HttpMethod.GET, "/web-app/countries").permitAll()
                .requestMatchers(HttpMethod.GET, "/web-app/trading-accounts/{tradingAccountId}").permitAll()
                .requestMatchers(WEB_APP_MATCHER).authenticated()
                .requestMatchers("/ws/**").permitAll()
                .anyRequest().authenticated()
        }
        .exceptionHandling { it.authenticationEntryPoint(HttpStatusEntryPoint(HttpStatus.UNAUTHORIZED)) }
        .sessionManagement { it.sessionCreationPolicy(SessionCreationPolicy.STATELESS) }
        .also {
            if (requestLoggingEnabled) {
                it.addFilterBefore(
                    LoggingFilter(
                        requestMatcher = OrRequestMatcher(WEB_APP_MATCHER, ADMIN_ROLE_EPS_MATCHER, SUPER_ADMIN_ROLE_EPS_MATCHER)
                    ),
                    UsernamePasswordAuthenticationFilter::class.java
                )
            }
        }
        .addFilterBefore(
            HmacSignatureFilter(
                requestMatcher = FIREBASE_WEBHOOK_CREATE_USER_MATCHER,
                secretKey = firebaseSecretKey,
                signatureHeaderName = "Signature",
                generateHmacSignature = Mac::generateBase64Hmac
            ),
            UsernamePasswordAuthenticationFilter::class.java
        )
        .addFilterBefore(
            HmacSignatureFilter(
                requestMatcher = SUMSUB_WEBHOOK_MATCHER,
                secretKey = sumsubSecretKey,
                signatureHeaderName = "x-payload-digest",
                generateHmacSignature = Mac::generateLowerCaseHexHmac
            ),
            UsernamePasswordAuthenticationFilter::class.java
        )
        .addFilterBefore(
            HmacSignatureFilter(
                requestMatcher = VERIFF_WEBHOOK_MATCHER,
                secretKey = veriffSecretKey,
                signatureHeaderName = "X-HMAC-SIGNATURE",
                generateHmacSignature = Mac::generateLowerCaseHexHmac
            ),
            UsernamePasswordAuthenticationFilter::class.java
        )
        .addFilterBefore(
            TrustPaySignatureFilter(
                requestMatcher = TRUST_PAY_WEBHOOK_PAYMENT_RESULT_MATCHER,
                secretKey = trustPaySecretKey
            ),
            UsernamePasswordAuthenticationFilter::class.java
        )
        .addFilterBefore(
            StripeSignatureFilter(
                requestMatcher = STRIPE_WEBHOOK_EVENTS_MATCHER,
                secretKey = stripeSecretKey
            ),
            UsernamePasswordAuthenticationFilter::class.java
        )
        .addFilterBefore(
            MyFatoorahSignatureFilter(
                requestMatcher = MY_FATOORAH_WEBHOOK_EVENTS_MATCHER,
                secretKey = myFatoorahSecretKey
            ),
            UsernamePasswordAuthenticationFilter::class.java
        )
        .addFilterBefore(
            ApiKeyAuthFilter(
                requestMatcher = DX_FEED_WEBHOOK_EVENTS_MATCHER,
                apiKeyHeader = "Authorization",
                apiKey = dxFeedSecretKey
            ),
            UsernamePasswordAuthenticationFilter::class.java
        )
        .addFilterBefore(
            ConfirmoSignatureFilter(
                requestMatcher = CONFIRMO_WEBHOOK_INVOICES_MATCHER,
                secretKey = confirmoSecretKey
            ),
            UsernamePasswordAuthenticationFilter::class.java
        )
        .addFilterBefore(
            ApiKeyAuthFilter(
                requestMatcher = DOCUSEAL_WEBHOOK_FORM_EVENTS_MATCHER,
                apiKeyHeader = "Authorization",
                apiKey = docuSealSecretKey
            ),
            UsernamePasswordAuthenticationFilter::class.java
        )
        .addFilterBefore(
            PaytikoSignatureFilter(
                requestMatcher = PAYTIKO_WEBHOOK_PAYMENT_RESULT_MATCHER,
                secretKey = paytikoSecretKey
            ),
            UsernamePasswordAuthenticationFilter::class.java
        )
        .addFilterBefore(
            NuveiSignatureFilter(
                requestMatcher = NUVEI_WEBHOOK_PAYMENT_RESULT_MATCHER,
                secretKey = nuveiSecretKey
            ),
            UsernamePasswordAuthenticationFilter::class.java
        )
        .addFilterBefore(
            TapSignatureFilter(
                requestMatcher = TAP_WEBHOOK_PAYMENT_RESULT_MATCHER,
                secretKey = tapSecretKey
            ),
            UsernamePasswordAuthenticationFilter::class.java
        )
        .addFilterBefore(
            CalendlySignatureFilter(
                requestMatcher = CALENDLY_WEBHOOK_EVENTS_MATCHER,
                secretKey = calendlySecretKey
            ),
            UsernamePasswordAuthenticationFilter::class.java
        )
        .addFilterAfter(
            AfterLoginUserUpdateActionFilter(
                requestMatcher = UPDATE_USER_LAST_LOGIN_IP_MATCHER,
                afterLoginAction = { userId, ipAddress, countryIsoCode ->
                    commandBus(
                        UpdateUserLastLoginIpCommand(
                            userId = userId,
                            lastLoginIp = ipAddress,
                            countryIsoCode = countryIsoCode
                        )
                    )
                }
            ),
            BearerTokenAuthenticationFilter::class.java
        )
        .oauth2ResourceServer { oauth2 ->
            oauth2.jwt { jwt -> jwt.jwtAuthenticationConverter(Jwt::toJwtUserAuthToken) }
        }
        .build()
}

private const val API_DOCS_PATH = "/api-docs/**"
private const val ACTUATOR_PATH = "/actuator/**"
private val WEB_APP_MATCHER = AntPathRequestMatcher.antMatcher("/web-app/**")
private val SUPER_ADMIN_ROLE_EPS_MATCHER = AntPathRequestMatcher.antMatcher("/super-admin-app/**")
private val ADMIN_ROLE_EPS_MATCHER = AntPathRequestMatcher.antMatcher("/admin-app/**")
private val SUPPORT_ROLE_EPS_MATCHER = OrRequestMatcher(
    AntPathRequestMatcher.antMatcher(HttpMethod.PATCH, "/admin-app/users/{userId}"),
    AntPathRequestMatcher.antMatcher(HttpMethod.GET, "/admin-app/users/{userId}"),
    AntPathRequestMatcher.antMatcher(HttpMethod.PUT, "/admin-app/users/{userId}"),
    AntPathRequestMatcher.antMatcher(HttpMethod.POST, "/admin-app/users"),
    AntPathRequestMatcher.antMatcher(HttpMethod.POST, "/admin-app/users/{userId}/transactions"),
    AntPathRequestMatcher.antMatcher(HttpMethod.POST, "/admin-app/users/search"),
    AntPathRequestMatcher.antMatcher(HttpMethod.GET, "/admin-app/users/{userId}/custom-token"),
    AntPathRequestMatcher.antMatcher(HttpMethod.POST, "/admin-app/users/{userId}/affiliate"),
    AntPathRequestMatcher.antMatcher(HttpMethod.PATCH, "/admin-app/users/{userId}/affiliate"),
    AntPathRequestMatcher.antMatcher(HttpMethod.POST, "/admin-app/users/{userId}/affiliate"),
    AntPathRequestMatcher.antMatcher(HttpMethod.PUT, "/admin-app/users/{userId}/email"),
    AntPathRequestMatcher.antMatcher(HttpMethod.POST, "/admin-app/orders/search"),
    AntPathRequestMatcher.antMatcher(HttpMethod.GET, "/admin-app/orders/{orderId}"),
    AntPathRequestMatcher.antMatcher(HttpMethod.POST, "/admin-app/discount-codes"),
    AntPathRequestMatcher.antMatcher(HttpMethod.POST, "/admin-app/discount-codes/search"),
    AntPathRequestMatcher.antMatcher(HttpMethod.GET, "/admin-app/discount-codes/{discountCodeId}"),
    AntPathRequestMatcher.antMatcher(HttpMethod.PATCH, "/admin-app/discount-codes/{discountCodeId}"),
    AntPathRequestMatcher.antMatcher(HttpMethod.POST, "/admin-app/trading-accounts/search"),
    AntPathRequestMatcher.antMatcher(HttpMethod.POST, "/admin-app/trading-accounts/for-review/search"),
    AntPathRequestMatcher.antMatcher(HttpMethod.GET, "/admin-app/trading-accounts/{tradingAccountId}"),
    AntPathRequestMatcher.antMatcher(HttpMethod.PUT, "/admin-app/trading-accounts/{tradingAccountId}/balance"),
    AntPathRequestMatcher.antMatcher(HttpMethod.POST, "/admin-app/trading-accounts/{tradingAccountId}/upgrade"),
    AntPathRequestMatcher.antMatcher(HttpMethod.POST, "/admin-app/trading-accounts/{tradingAccountId}/breach"),
    AntPathRequestMatcher.antMatcher(HttpMethod.DELETE, "/admin-app/trading-accounts/{tradingAccountId}/breach"),
    AntPathRequestMatcher.antMatcher(HttpMethod.PATCH, "/admin-app/trading-accounts/{tradingAccountId}"),
    AntPathRequestMatcher.antMatcher(HttpMethod.POST, "/admin-app/trading-accounts/{tradingAccountId}/migrate-platform"),
    AntPathRequestMatcher.antMatcher(HttpMethod.POST, "/admin-app/users/affiliates/search"),
    AntPathRequestMatcher.antMatcher(HttpMethod.POST, "/admin-app/challenges/search"),
    AntPathRequestMatcher.antMatcher(HttpMethod.POST, "/admin-app/labels/search")
)
private val FIREBASE_WEBHOOK_CREATE_USER_MATCHER =
    AntPathRequestMatcher.antMatcher(HttpMethod.POST, "/webhooks/firebase/users")
private val SUMSUB_WEBHOOK_MATCHER =
    AntPathRequestMatcher.antMatcher(HttpMethod.POST, "/webhooks/sumsub/**")
private val VERIFF_WEBHOOK_MATCHER =
    AntPathRequestMatcher.antMatcher(HttpMethod.POST, "/webhooks/veriff/**")
private val TRUST_PAY_WEBHOOK_PAYMENT_RESULT_MATCHER =
    AntPathRequestMatcher.antMatcher(HttpMethod.GET, "/webhooks/trust-pay")
private val STRIPE_WEBHOOK_EVENTS_MATCHER = AntPathRequestMatcher.antMatcher(HttpMethod.POST, "/webhooks/stripe/events")
private val MY_FATOORAH_WEBHOOK_EVENTS_MATCHER = AntPathRequestMatcher.antMatcher(HttpMethod.POST, "/webhooks/my-fatoorah/events")
private val DX_FEED_WEBHOOK_EVENTS_MATCHER = AntPathRequestMatcher.antMatcher(HttpMethod.POST, "/webhooks/dx-feed/**")
private val CONFIRMO_WEBHOOK_INVOICES_MATCHER =
    AntPathRequestMatcher.antMatcher(HttpMethod.POST, "/webhooks/confirmo/invoice-status")
private val DOCUSEAL_WEBHOOK_FORM_EVENTS_MATCHER =
    AntPathRequestMatcher.antMatcher(HttpMethod.POST, "/webhooks/docuseal/forms")
private val PAYTIKO_WEBHOOK_PAYMENT_RESULT_MATCHER =
    AntPathRequestMatcher.antMatcher(HttpMethod.POST, "/webhooks/paytiko/payments")
private val NUVEI_WEBHOOK_PAYMENT_RESULT_MATCHER =
    AntPathRequestMatcher.antMatcher(HttpMethod.POST, "/webhooks/nuvei")
private val TAP_WEBHOOK_PAYMENT_RESULT_MATCHER =
    AntPathRequestMatcher.antMatcher(HttpMethod.POST, "/webhooks/tap/**")
private val WAITING_LIST_MATCHER = AntPathRequestMatcher.antMatcher(HttpMethod.POST, "/web-app/waiting-list")
private val CALENDLY_WEBHOOK_EVENTS_MATCHER = AntPathRequestMatcher.antMatcher(HttpMethod.POST, "/webhooks/calendly/events")

private val UPDATE_USER_LAST_LOGIN_IP_MATCHER = AntPathRequestMatcher.antMatcher(HttpMethod.GET, "/web-app/users/me")
