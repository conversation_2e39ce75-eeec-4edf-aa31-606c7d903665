package com.cleevio.fortraders.domain.model.tournament.exception

import com.cleevio.fortraders.domain.model.tournamentreward.constant.TournamentRewardType
import com.cleevio.fortraders.infrastructure.exception.CustomErrorReasonTypes
import com.cleevio.fortraders.infrastructure.exception.ForTradersApiException
import org.springframework.http.HttpStatus
import org.springframework.web.bind.annotation.ResponseStatus

@ResponseStatus(HttpStatus.BAD_REQUEST)
class TournamentWithInvalidInitialPrizePoolException : ForTradersApiException(
    reason = CustomErrorReasonTypes.TOURNAMENT_WITH_INVALID_INITIAL_PRIZE_POOL,
    message = "Initial prize pool is not specified for ${TournamentRewardType.MONEY}."
)
