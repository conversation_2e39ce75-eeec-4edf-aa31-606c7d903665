package com.cleevio.fortraders.domain.model.tournamentreward

import com.cleevio.fortraders.domain.UpdatableEntity
import com.cleevio.fortraders.domain.model.tournamentreward.constant.TournamentRewardType
import com.cleevio.fortraders.domain.model.tournamentreward.exception.TournamentRewardRequiredValuesInvalidException
import jakarta.persistence.Entity
import jakarta.persistence.EnumType
import jakarta.persistence.Enumerated
import java.util.UUID

@Entity
class TournamentReward(
    val tournamentId: UUID,
    val name: String,
    @Enumerated(EnumType.STRING)
    val type: TournamentRewardType,
    val position: Int,
    val challengePlanId: UUID? = null,
) : UpdatableEntity() {

    init {
        if (type == TournamentRewardType.CHALLENGES && challengePlanId == null) {
            throw TournamentRewardRequiredValuesInvalidException()
        }
    }
}
