package com.cleevio.fortraders.domain.model.mailtemplate.constant

enum class MailType {
    PAYMENT_SUCCESS,
    PAYMENT_FAILED,

    WITHDRAWAL_REQUEST_CREATED,
    WITHDRAWAL_REQUEST_APPROVED,
    WIT<PERSON><PERSON>WAL_REQUEST_REJECTED,

    TOURNAMENT_STARTED,
    <PERSON>URNA<PERSON><PERSON>_ENDED,
    TOURNA<PERSON><PERSON>_RULE<PERSON>_BREACHED,

    EVALUATION_TWO_STEP_STANDARD_CHALLENGE_PHASE_ACTIVATED,
    EVALUATION_THREE_STEP_STANDARD_CHALLENGE_PHASE_ACTIVATED,
    EVALUATION_THREE_STEP_PRO_CHALLENGE_PHASE_ACTIVATED,
    EVALUATION_FOUR_STEP_STANDARD_CHALLENGE_PHASE_ACTIVATED,

    FUNDED_INSTANT_CHALLENGE_PHASE_ACTIVATED,
    FUNDED_TWO_STEP_STANDARD_CHALLENGE_PHASE_ACTIVATED,
    FUNDED_THREE_STEP_STANDARD_CHALLENGE_PHASE_ACTIVATED,
    FUNDED_THREE_STEP_PRO_CHALLENGE_PHASE_ACTIVATED,
    FUNDED_FOUR_STEP_STANDARD_CHALLENGE_PHASE_ACTIVATED,

    PAYOUT_DECLINED,
    PAYOUT_APPROVED,
    PAYOUT_CREATED,

    ACCOUNT_DAILY_PAUSED,
    ACCOUNT_DAILY_PAUSED_SECOND_TIME,
    ACCOUNT_REACTIVATED,
    ACCOUNT_DAILY_PROFIT_CAP_PAUSED,
    ACCOUNT_BREACHED,
    ACCOUNT_INACTIVITY_AFTER_BREACHED,

    SUBSCRIPTION_CANCELLED,
    SUBSCRIPTION_PAYMENT_SUCCESS,
    SUBSCRIPTION_PAYMENT_FAILED,

    USER_CONTRACT_CREATED,

    ORDER_PROCESS_ABANDONED,

    TOP_RANKING_USER,

    VERIFICATION_CALL_REQUEST,

    ADMIN_TOPUP_WALLET,
    ;

    fun getVariables(): List<MailVariables> = when (this) {
        PAYMENT_SUCCESS, PAYMENT_FAILED -> listOf(
            MailVariables.PAYMENT_AMOUNT,
            MailVariables.PAYMENT_DATE,
            MailVariables.PAYMENT_TYPE,
            MailVariables.PAYMENT_IS_SUBSCRIPTION
        )

        WITHDRAWAL_REQUEST_CREATED, WITHDRAWAL_REQUEST_APPROVED, WITHDRAWAL_REQUEST_REJECTED -> listOf(
            MailVariables.WITHDRAWAL_AMOUNT,
            MailVariables.WITHDRAWAL_DATE,
            MailVariables.WITHDRAWAL_METHOD,
            MailVariables.WITHDRAWAL_REJECT_REASON
        )

        TOURNAMENT_STARTED, TOURNAMENT_ENDED, TOURNAMENT_RULES_BREACHED -> listOf(
            MailVariables.TOURNAMENT_NAME,
            MailVariables.TOURNAMENT_BREACH_DATE
        )

        EVALUATION_TWO_STEP_STANDARD_CHALLENGE_PHASE_ACTIVATED,
        EVALUATION_THREE_STEP_STANDARD_CHALLENGE_PHASE_ACTIVATED,
        EVALUATION_THREE_STEP_PRO_CHALLENGE_PHASE_ACTIVATED,
        EVALUATION_FOUR_STEP_STANDARD_CHALLENGE_PHASE_ACTIVATED,

        FUNDED_INSTANT_CHALLENGE_PHASE_ACTIVATED,
        FUNDED_TWO_STEP_STANDARD_CHALLENGE_PHASE_ACTIVATED,
        FUNDED_THREE_STEP_STANDARD_CHALLENGE_PHASE_ACTIVATED,
        FUNDED_THREE_STEP_PRO_CHALLENGE_PHASE_ACTIVATED,
        FUNDED_FOUR_STEP_STANDARD_CHALLENGE_PHASE_ACTIVATED,

        ACCOUNT_DAILY_PAUSED,
        ACCOUNT_DAILY_PAUSED_SECOND_TIME,
        ACCOUNT_REACTIVATED,
        ACCOUNT_DAILY_PROFIT_CAP_PAUSED,
        -> TRADING_ACCOUNT_MAIL_VARIABLES

        ACCOUNT_BREACHED -> TRADING_ACCOUNT_MAIL_VARIABLES + listOf(
            MailVariables.BREACH_DISCOUNT_CODE,
            MailVariables.BREACH_DISCOUNT_CODE_PERCENTAGE,
            MailVariables.BREACH_EXTERNAL_REASON,
            MailVariables.BREACH_ACCOUNT_EQUITY,
            MailVariables.BREACH_DATE
        )

        PAYOUT_CREATED, PAYOUT_APPROVED, PAYOUT_DECLINED -> listOf(
            MailVariables.PAYOUT_AMOUNT,
            MailVariables.PAYOUT_BONUS_AMOUNT,
            MailVariables.PAYOUT_DATE,
            MailVariables.PAYOUT_DECLINE_REASON
        )

        SUBSCRIPTION_CANCELLED, SUBSCRIPTION_PAYMENT_SUCCESS, SUBSCRIPTION_PAYMENT_FAILED -> TRADING_ACCOUNT_MAIL_VARIABLES

        USER_CONTRACT_CREATED -> listOf(
            MailVariables.ACCOUNT_ORDER_ID
        )

        ACCOUNT_INACTIVITY_AFTER_BREACHED -> TRADING_ACCOUNT_MAIL_VARIABLES + listOf(
            MailVariables.DISCOUNT_CODE_PERCENTAGE,
            MailVariables.DISCOUNT_CODE
        )

        ORDER_PROCESS_ABANDONED -> listOf(
            MailVariables.ACCOUNT_ORDER_ID,
            MailVariables.DISCOUNT_CODE_PERCENTAGE,
            MailVariables.DISCOUNT_CODE,
            MailVariables.ACCOUNT_PLAN_NAME
        )

        TOP_RANKING_USER -> listOf(
            MailVariables.TOP_PERCENTAGE,
            MailVariables.PERFORMANCE_REASON,
            MailVariables.PERFORMANCE_DATE,
            MailVariables.IMAGE_HTML_ELEMENT
        )

        VERIFICATION_CALL_REQUEST -> listOf(
            MailVariables.BOOKING_URL,
            MailVariables.EMAIL_CONTENT
        )

        ADMIN_TOPUP_WALLET -> listOf(
            MailVariables.TOPUP_AMOUNT,
            MailVariables.TOPUP_NOTE
        )
    } + listOf(
        MailVariables.USER_FIRST_NAME,
        MailVariables.USER_LAST_NAME
    )

    fun getLinks(): List<MailLinks> = when (this) {
        TOURNAMENT_STARTED, TOURNAMENT_ENDED, TOURNAMENT_RULES_BREACHED -> listOf(
            MailLinks.TOURNAMENT_URL
        )
        VERIFICATION_CALL_REQUEST -> listOf(MailLinks.VERIFICATION_CALL_BOOKING_URL)

        else -> emptyList()
    } + listOf(
        MailLinks.DASHBOARD_URL,
        MailLinks.LOGIN_URL,
        MailLinks.PURCHASE_CHALLENGE_URL
    )
}

private val TRADING_ACCOUNT_MAIL_VARIABLES = listOf(
    MailVariables.ACCOUNT_ID,
    MailVariables.ACCOUNT_ORDER_ID,
    MailVariables.ACCOUNT_PLAN_NAME,
    MailVariables.ACCOUNT_CHALLENGE_STEP_NUMBER,
    MailVariables.ACCOUNT_STARTING_BALANCE,
    MailVariables.ACCOUNT_SERVER,
    MailVariables.ACCOUNT_SERVER_LOGIN,
    MailVariables.ACCOUNT_SERVER_PASSWORD,
    MailVariables.ACCOUNT_PROFIT_TARGET,
    MailVariables.ACCOUNT_MAX_DRAWDOWN,
    MailVariables.ACCOUNT_DAILY_DRAWDOWN,
    MailVariables.ACCOUNT_MIN_PROFITABLE_TRADING_DAYS,
    MailVariables.ACCOUNT_MAX_TRADING_DAYS,
    MailVariables.ACCOUNT_PROFIT_SPLIT,
    MailVariables.ACCOUNT_CONSISTENCY_TARGET
)
