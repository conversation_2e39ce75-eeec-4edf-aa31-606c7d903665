package com.cleevio.fortraders.domain.model.userverification

import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.util.UUID

@Service
class UserVerificationCreateService(
    private val userVerificationRepository: UserVerificationRepository,
) {
    @Transactional
    fun create(userId: UUID, verificationData: Map<String, Any?>): UserVerification = userVerificationRepository.save(
        UserVerification(
            userId = userId,
            verificationData = verificationData
        )
    )
}
