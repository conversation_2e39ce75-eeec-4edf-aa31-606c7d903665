package com.cleevio.fortraders.domain.model.tradingaccountactivity

import jakarta.transaction.Transactional
import org.springframework.stereotype.Service
import java.time.Instant
import java.util.UUID

@Service
class TradingAccountActivityCreateService(
    private val tradingAccountActivityRepository: TradingAccountActivityRepository,
) {
    @Transactional
    fun createOrNothing(tradingAccountId: UUID, ipAddress: String, countryId: UUID?, time: Instant) {
        tradingAccountActivityRepository.createOrNothing(
            id = UUID.randomUUID(),
            tradingAccountId = tradingAccountId,
            ipAddress = ipAddress,
            countryId = countryId,
            time = time,
            createdAt = Instant.now()
        )
    }
}
