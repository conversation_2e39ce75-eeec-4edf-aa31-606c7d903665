package com.cleevio.fortraders.domain.model.tradingaccountactivity.exception

import com.cleevio.fortraders.infrastructure.exception.CustomErrorReasonTypes
import com.cleevio.fortraders.infrastructure.exception.ForTradersApiException
import org.springframework.http.HttpStatus
import org.springframework.web.bind.annotation.ResponseStatus

@ResponseStatus(HttpStatus.NOT_FOUND)
class TradingAccountActivityNotFoundException : ForTradersApiException(
    reason = CustomErrorReasonTypes.TRADING_ACCOUNT_ACTIVITY_NOT_FOUND,
    message = "TradingAccountActivity not found."
)
