package com.cleevio.fortraders.domain.model.tournamenttrade

import com.cleevio.fortraders.domain.BaseRepository
import com.cleevio.fortraders.domain.model.tournamenttrade.constant.TournamentTradeState
import org.springframework.data.jpa.repository.Query
import org.springframework.stereotype.Repository
import java.time.Instant
import java.util.UUID

@Deprecated("Used in V1, not used in V2")
@Repository
interface TournamentTradeRepository : BaseRepository<TournamentTrade> {

    fun findByIdAndState(id: UUID, state: TournamentTradeState): TournamentTrade?

    fun findByIdAndUserIdAndStateIs(id: UUID, userId: UUID, state: TournamentTradeState): TournamentTrade?

    fun findAllByUserIdAndTournamentIdAndState(
        userId: UUID,
        tournamentId: UUID,
        state: TournamentTradeState,
    ): List<TournamentTrade>

    fun findAllByUserIdInAndTournamentIdAndState(
        userIds: Set<UUID>,
        tournamentId: UUID,
        state: TournamentTradeState,
    ): List<TournamentTrade>

    fun findAllByTournamentIdAndStateIs(tournamentId: UUID, state: TournamentTradeState): List<TournamentTrade>

    @Query(
        """
        SELECT tt
        FROM TournamentTrade tt, Tournament t
        WHERE tt.tournamentId = t.id
        AND t.endsAt >= :endsAt
        """
    )
    fun findAllWhereTournamentEndsAtGreaterThanEqual(endsAt: Instant): List<TournamentTrade>
}
