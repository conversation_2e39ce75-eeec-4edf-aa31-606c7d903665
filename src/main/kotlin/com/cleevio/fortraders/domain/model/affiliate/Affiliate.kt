package com.cleevio.fortraders.domain.model.affiliate

import com.cleevio.fortraders.application.common.util.normalize
import com.cleevio.fortraders.application.common.util.percentageToDecimal
import com.cleevio.fortraders.domain.UpdatableEntity
import com.cleevio.fortraders.domain.model.affiliate.constant.AffiliateState
import com.cleevio.fortraders.domain.model.affiliate.exception.AffiliateCommissionBalanceNotEnoughException
import jakarta.persistence.Entity
import jakarta.persistence.EnumType
import jakarta.persistence.Enumerated
import java.math.BigDecimal
import java.time.Duration
import java.time.Instant
import java.util.Optional
import java.util.UUID
import kotlin.jvm.optionals.getOrNull

@Entity
class Affiliate(
    val userId: UUID,
    couponCode: String,
    discountAmountPercentage: Int,
    commissionPercentage: Int,
    note: String?,
    durationBetweenAffiliatePayouts: Duration,
) : UpdatableEntity() {
    var couponCode: String = couponCode.uppercase()
        private set

    var discountAmountPercentage: Int = discountAmountPercentage
        private set

    var commissionPercentage: Int = commissionPercentage
        private set

    var note: String? = note
        private set

    @Enumerated(EnumType.STRING)
    var state: AffiliateState = AffiliateState.ACTIVE
        private set

    var commissionBalance: BigDecimal = BigDecimal.ZERO
        private set

    var nextAffiliatePayoutAvailableAt: Instant = this.createdAt + durationBetweenAffiliatePayouts
        private set

    fun calculateDiscountedPrice(basePrice: BigDecimal): BigDecimal =
        basePrice * (BigDecimal.ONE - discountAmountPercentage.percentageToDecimal())

    fun calculateCommission(basePrice: BigDecimal): BigDecimal = basePrice * commissionPercentage.percentageToDecimal()

    fun addToCommissionBalance(amount: BigDecimal) {
        commissionBalance += amount
    }

    fun deductFromCommissionAfterApprovalBalance(
        amount: BigDecimal,
        lastApprovedAffiliatePayoutCreatedAt: Instant,
        durationBetweenAffiliatePayouts: Duration,
    ) {
        if (amount > commissionBalance) throw AffiliateCommissionBalanceNotEnoughException()
        this.commissionBalance -= amount
        this.nextAffiliatePayoutAvailableAt = lastApprovedAffiliatePayoutCreatedAt + durationBetweenAffiliatePayouts
    }

    fun checkHasEnoughCommissionBalance(amount: BigDecimal) {
        if (amount > commissionBalance) throw AffiliateCommissionBalanceNotEnoughException()
    }

    fun patchAdminSpecifiedProperties(
        state: AffiliateState? = null,
        couponCode: String? = null,
        commissionPercentage: Int? = null,
        discountAmountPercentage: Int? = null,
        note: Optional<String>? = null,
    ) {
        state?.let { this.state = it }
        couponCode?.let { this.couponCode = it.uppercase() }
        commissionPercentage?.let { this.commissionPercentage = it }
        discountAmountPercentage?.let { this.discountAmountPercentage = it }
        note?.let { this.note = it.getOrNull() }
    }

    fun isPropFirmMatchAffiliate() = this.couponCode.normalize() == PROP_FIRM_MATCH_AFFILIATE_COUPON_CODE
}

private const val PROP_FIRM_MATCH_AFFILIATE_COUPON_CODE = "match"
