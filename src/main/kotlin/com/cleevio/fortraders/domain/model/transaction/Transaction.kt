package com.cleevio.fortraders.domain.model.transaction

import com.cleevio.fortraders.domain.UpdatableEntity
import com.cleevio.fortraders.domain.model.transaction.constant.PaymentGatewayProvider
import com.cleevio.fortraders.domain.model.transaction.constant.TransactionStatus
import com.cleevio.fortraders.domain.model.transaction.constant.TransactionType
import jakarta.persistence.Entity
import jakarta.persistence.EnumType
import jakarta.persistence.Enumerated
import java.math.BigDecimal
import java.time.Duration
import java.time.Instant
import java.util.UUID

@Entity
class Transaction(
    val walletId: UUID,
    val orderId: UUID?,

    @Enumerated(EnumType.STRING)
    val type: TransactionType,
    val amount: BigDecimal,
    status: TransactionStatus?,
    val note: String?,
) : UpdatableEntity() {
    init {
        require(type == TransactionType.RECURRING_DEPOSIT || status == null) {
            "Transaction initial status can be set only for recurring deposits"
        }
    }

    @Enumerated(EnumType.STRING)
    var status: TransactionStatus =
        status ?: if (type.isInstantCompleted()) TransactionStatus.COMPLETED else TransactionStatus.REQUESTED
        private set

    @Enumerated(EnumType.STRING)
    var provider: PaymentGatewayProvider? = null
        private set

    var providerPaymentId: String? = null
        private set

    var providerSubscriptionId: String? = null
        private set

    var nextPaymentAt: Instant? = null
        private set

    fun registerProviderPayment(provider: PaymentGatewayProvider, providerPaymentId: String) {
        this.provider = provider
        this.providerPaymentId = providerPaymentId
    }

    fun registerProviderSubscription(
        provider: PaymentGatewayProvider,
        providerSubscriptionId: String,
        subscriptionInterval: Duration,
    ) {
        require(this.provider == null || this.provider == provider) { "Provider is already registered" }
        this.provider = provider
        this.providerSubscriptionId = providerSubscriptionId
        this.nextPaymentAt = if (status.isFinalFailure()) null else Instant.now().plus(subscriptionInterval)
    }

    fun isSubscriptionActive(): Boolean = this.nextPaymentAt != null

    fun cancelNextPayment() {
        this.nextPaymentAt = null
    }

    fun willStatusChange(status: TransactionStatus): Boolean = this.status != TransactionStatus.COMPLETED && this.status != status

    fun changeStatus(status: TransactionStatus): Boolean {
        if (this.status == status) return false
        require(this.status != TransactionStatus.COMPLETED) { "Transaction is already completed!" }
        this.status = status
        return true
    }
}

private fun TransactionType.isInstantCompleted(): Boolean = when (this) {
    TransactionType.ADMIN_DEPOSIT,
    TransactionType.TOURNAMENT_PRIZE_DEPOSIT,
    TransactionType.TOURNAMENT_BUY_IN,
    TransactionType.WITHDRAWAL,
    TransactionType.WITHDRAWAL_FEE,
    TransactionType.CHALLENGE_ORDER,
    TransactionType.CHALLENGE_PAYOUT,
    TransactionType.CHALLENGE_PAYOUT_BONUS,
    TransactionType.AFFILIATE_PAYOUT,
    TransactionType.CREDIT_USAGE,
    -> true

    TransactionType.DEPOSIT, TransactionType.RECURRING_DEPOSIT -> false
}
