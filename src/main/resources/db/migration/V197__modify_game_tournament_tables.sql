DELETE FROM prize_distribution_snapshot;
DELETE FROM tournament_user;
DELETE FROM tournament_trade;
DELETE FROM tournament_instrument;
DELETE FROM instrument;
DELETE FROM tournament;

DELETE FROM game;
DELETE FROM file WHERE id IN (SELECT logo_file_id FROM game);

ALTER TABLE tournament_trade
    DROP COLUMN instrument_id;

DROP TABLE tournament_instrument;
DROP TABLE instrument;
DROP TABLE prize_distribution_snapshot;

ALTER TABLE game
    ADD COLUMN group_mt5 TEXT;

ALTER TABLE game
    DROP COLUMN type;

ALTER TABLE tournament
    ALTER COLUMN entry_fee DROP NOT NULL;

ALTER TABLE tournament
    RENAME COLUMN reward to reward_type;

ALTER TABLE tournament
    ADD COLUMN starting_balance NUMERIC NOT NULL;

ALTER TABLE tournament
    DROP COLUMN leverage_enabled,
    DROP COLUMN initial_prize_pool,
    DROP COLUMN prize_pool_type;

CREATE TABLE tournament_reward
(
    id                  UUID    PRIMARY KEY,
    tournament_id       UUID    NOT NULL,
    name                TEXT    NOT NULL,
    type                TEXT    NOT NULL,
    position            INTEGER NOT NULL,
    challenge_plan_id   UUID,
    created_at          TIMESTAMPTZ NOT NULL,
    updated_at          TIMESTAMPTZ NOT NULL,
    created_by          UUID,
    updated_by          UUID,

    CONSTRAINT "b96f76d13b1f4d34bf79_fk" FOREIGN KEY ("tournament_id") REFERENCES "tournament" ("id")
);
